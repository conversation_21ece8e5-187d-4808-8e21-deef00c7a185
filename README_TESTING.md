# 🧪 Testing Guide - AI Home Improvement Platform

## 📋 Test Suite Overview

This comprehensive test suite covers all aspects of the AI-powered home improvement platform, ensuring reliability, maintainability, and excellent user experience.

### 🎯 **Test Coverage**

| Component | Files | Coverage |
|-----------|-------|----------|
| **Controllers** | 5 specs | AI Chat, Project AI, Static, Authentication |
| **Models** | 6 specs | AiAgent, Property, Project, User, Address, Task |
| **Services** | 2 specs | AiService, OpenAI Integration |
| **System Tests** | 5 specs | End-to-end user workflows |
| **Integration** | 1 spec | Cross-feature workflows |
| **Support** | 3 helpers | Authentication, AI mocking, Test utilities |

## 🚀 **Running Tests**

### Quick Start
```bash
# Setup test environment (first time only)
bin/test setup

# Run all tests
bin/test

# Run specific test types
bin/test unit        # Models, controllers, services
bin/test system      # End-to-end browser tests
bin/test integration # Cross-feature workflows
bin/test ai          # AI-specific functionality
bin/test fast        # Quick unit tests only
bin/test coverage    # With coverage report
bin/test ci          # CI/CD pipeline
```

### Manual RSpec Commands
```bash
# Full suite
bundle exec rspec

# Specific files
bundle exec rspec spec/models/ai_agent_spec.rb
bundle exec rspec spec/system/ai_chat_spec.rb

# With tags
bundle exec rspec --tag ai
bundle exec rspec --tag ~slow

# Parallel execution
bundle exec parallel_rspec spec/
```

## 🎯 **Key Test Scenarios**

### 🤖 **AI Features**
- ✅ **Chat FAB**: Floating action button functionality
- ✅ **Real-time Chat**: Message sending/receiving with Jack
- ✅ **Project Planning**: Conversational project creation
- ✅ **Context Awareness**: Property and project context
- ✅ **Error Handling**: API failures and recovery
- ✅ **Mock Responses**: Development mode behavior

### 🔐 **Authentication & Security**
- ✅ **Landing Page**: Authenticated vs unauthenticated behavior
- ✅ **Protected Routes**: Access control enforcement
- ✅ **Session Management**: Login/logout workflows
- ✅ **Invalid Sessions**: Expired token handling
- ✅ **Cross-Feature Auth**: Consistent authentication

### 🧭 **Navigation & UX**
- ✅ **Cross-Navigation**: Between AI features
- ✅ **Breadcrumbs**: Back to dashboard functionality
- ✅ **User Menu**: Dropdown navigation
- ✅ **Mobile Support**: Responsive design
- ✅ **Loading States**: User feedback during AI processing

### 📊 **Data Management**
- ✅ **Property CRUD**: Create, read, update, delete
- ✅ **Project Lifecycle**: Planning to completion
- ✅ **Task Management**: Project task associations
- ✅ **Address Validation**: Nested attribute handling
- ✅ **User Relationships**: Proper data isolation

## 🛠️ **Test Configuration**

### Environment Setup
```ruby
# spec/rails_helper.rb
- Database cleaner for isolation
- Factory Bot for test data
- Capybara for system tests
- Shoulda matchers for validations
- Authentication helpers
- AI service mocking
```

### AI Service Mocking
```ruby
# In tests, AI responses are mocked by default
mock_ai_chat_response('Helpful response from Jack')
mock_ai_project_response(action: 'show_project_summary')
mock_ai_error # Simulate API failures
```

### Authentication Helpers
```ruby
# Quick user authentication in tests
user = create_authenticated_user
sign_in_as(user)
sign_out
```

## 📈 **Quality Standards**

### Coverage Targets
- **Models**: 95%+ line coverage
- **Controllers**: 90%+ line coverage
- **Services**: 95%+ line coverage
- **Critical Paths**: 100% coverage

### Performance Goals
- **Unit Tests**: < 0.1s each
- **Controller Tests**: < 0.5s each
- **System Tests**: < 5s each
- **Full Suite**: < 2 minutes

### Best Practices
- ✅ Factory Bot instead of fixtures
- ✅ Proper external service mocking
- ✅ Clear test descriptions
- ✅ DRY principles with shared examples
- ✅ Edge case coverage

## 🔧 **Development Workflow**

### Adding New Features
1. **Write failing tests first** (TDD approach)
2. **Add model specs** for data validation
3. **Add controller specs** for API endpoints
4. **Add system specs** for user workflows
5. **Update integration specs** for cross-feature impact

### Test-Driven Development
```bash
# 1. Write failing test
bundle exec rspec spec/models/new_feature_spec.rb

# 2. Implement feature
# ... code implementation ...

# 3. Make test pass
bundle exec rspec spec/models/new_feature_spec.rb

# 4. Refactor and ensure all tests pass
bin/test fast
```

### Debugging Tests
```bash
# Run with debugging
bundle exec rspec --backtrace spec/failing_spec.rb

# Focus on specific test
bundle exec rspec spec/file_spec.rb:42

# Use pry for debugging
# Add 'binding.pry' in test code
```

## 🚨 **Common Issues & Solutions**

### Database Issues
```bash
# Reset test database
bin/test setup

# Clear test data
rails db:test:prepare
```

### JavaScript Tests Failing
```bash
# Ensure Chrome is installed for headless testing
# Check Capybara configuration in spec/support/capybara.rb
```

### AI Service Tests
```bash
# Ensure AI services are properly mocked
# Check spec/support/ai_service_helpers.rb
```

### Slow Tests
```bash
# Run only fast tests during development
bin/test fast

# Use parallel execution for full suite
bundle exec parallel_rspec spec/
```

## 📊 **CI/CD Integration**

### GitHub Actions / CI Pipeline
```yaml
# .github/workflows/test.yml
- name: Setup Test Environment
  run: bin/test setup

- name: Run Test Suite
  run: bin/test ci

- name: Upload Coverage
  uses: codecov/codecov-action@v1
```

### Pre-commit Hooks
```bash
# Run fast tests before commit
bin/test fast

# Run full suite before push
bin/test
```

## 🎯 **Test Examples**

### Model Test
```ruby
RSpec.describe Property, type: :model do
  it { should belong_to(:user) }
  it { should validate_presence_of(:name) }
  
  it 'creates property with address' do
    property = create(:property)
    expect(property.address).to be_present
  end
end
```

### Controller Test
```ruby
RSpec.describe AiChatController, type: :controller do
  let(:user) { create(:user) }
  
  before { sign_in_as(user) }
  
  it 'returns AI response' do
    mock_ai_chat_response('Helpful advice')
    post :ask, params: { question: 'Help me' }
    
    expect(response).to be_successful
    expect(json_response['response']).to eq('Helpful advice')
  end
end
```

### System Test
```ruby
RSpec.describe 'AI Chat', type: :system do
  let(:user) { create_authenticated_user }
  
  it 'allows chatting with Jack', js: true do
    visit ai_chat_path
    fill_in 'Ask your question...', with: 'Help with kitchen'
    click_button 'Send'
    
    expect(page).to have_text('Help with kitchen')
    expect(page).to have_text('Helpful response')
  end
end
```

This comprehensive testing approach ensures the AI-powered platform is robust, reliable, and provides an excellent user experience! 🎉