#!/usr/bin/env ruby

# Comprehensive test runner for the AI-powered home improvement platform

require 'fileutils'

class TestRunner
  def self.run(args = ARGV)
    new.run(args)
  end

  def run(args)
    puts "🧪 AI Home Improvement Platform Test Suite"
    puts "=" * 50

    case args.first
    when 'setup'
      setup_test_environment
    when 'unit'
      run_unit_tests
    when 'system'
      run_system_tests
    when 'integration'
      run_integration_tests
    when 'ai'
      run_ai_tests
    when 'fast'
      run_fast_tests
    when 'coverage'
      run_with_coverage
    when 'ci'
      run_ci_suite
    else
      run_full_suite
    end
  end

  private

  def setup_test_environment
    puts "🔧 Setting up test environment..."
    
    system("rails db:environment:set RAILS_ENV=test") || exit(1)
    system("rails db:drop:_unsafe RAILS_ENV=test") || true
    system("rails db:create RAILS_ENV=test") || exit(1)
    system("rails db:migrate RAILS_ENV=test") || exit(1)
    
    puts "✅ Test environment ready!"
  end

  def run_unit_tests
    puts "🔬 Running unit tests (models, controllers, services)..."
    system("bundle exec rspec spec/models spec/controllers spec/services --format documentation")
  end

  def run_system_tests
    puts "🖥️  Running system tests (end-to-end)..."
    system("bundle exec rspec spec/system --format documentation")
  end

  def run_integration_tests
    puts "🔗 Running integration tests..."
    system("bundle exec rspec spec/integration --format documentation")
  end

  def run_ai_tests
    puts "🤖 Running AI-specific tests..."
    system("bundle exec rspec spec/services/ai_service_spec.rb spec/controllers/ai_chat_controller_spec.rb spec/controllers/project_ai_controller_spec.rb spec/system/ai_chat_spec.rb spec/system/project_ai_spec.rb --format documentation")
  end

  def run_fast_tests
    puts "⚡ Running fast tests (excluding system tests)..."
    system("bundle exec rspec spec/models spec/controllers spec/services --format progress")
  end

  def run_with_coverage
    puts "📊 Running tests with coverage report..."
    ENV['COVERAGE'] = 'true'
    system("bundle exec rspec --format documentation")
  end

  def run_ci_suite
    puts "🚀 Running CI test suite..."
    
    # Setup
    setup_test_environment
    
    # Run tests with coverage
    ENV['COVERAGE'] = 'true'
    success = system("bundle exec rspec --format progress --format RspecJunitFormatter --out tmp/rspec.xml")
    
    exit(success ? 0 : 1)
  end

  def run_full_suite
    puts "🎯 Running full test suite..."
    system("bundle exec rspec --format documentation")
  end
end

TestRunner.run if __FILE__ == $0