class CreateAiAgents < ActiveRecord::Migration[8.0]
  def change
    create_table :ai_agents, id: :uuid do |t|
      t.references :user, null: false, foreign_key: true, type: :uuid
      t.integer :provider, null: false, default: 0
      t.integer :model_type, null: false, default: 0
      t.string :api_key, null: true
      t.string :name, null: false, default: "Jack"
      t.text :system_prompt, null: true
      t.json :settings, default: {}
      
      t.timestamps
    end
    
    add_index :ai_agents, [:user_id, :provider]
  end
end