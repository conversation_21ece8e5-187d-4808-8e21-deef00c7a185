class CreateContractors < ActiveRecord::Migration[8.0]
  def change
    create_table :contractors, id: :uuid do |t|
      t.string :name, null: false
      t.string :email, null: false
      t.string :phone, null: false
      t.string :logo_url
      t.boolean :quick_hire, default: false
      t.string :website_url
      t.string :linkedin_url
      t.string :facebook_url
      t.string :instagram_url
      t.string :x_url

      t.belongs_to :trade, null: false, foreign_key: true, type: :uuid

      t.timestamps
    end
  end
end
