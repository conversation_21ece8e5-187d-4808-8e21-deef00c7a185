class CreateUsers < ActiveRecord::Migration[8.0]
  def change
    change_table :users, id: :uuid do |t|
      t.string :password_digest, null: false, default: ""
      t.boolean :verified, null: false, default: false

      t.boolean :otp_required_for_sign_in, null: false, default: false
      t.string :otp_secret, null: false, default: ""

      t.remove_index :confirmation_token
      t.remove_index :reset_password_token
      t.remove_index :unlock_token
      t.remove :encrypted_password, :reset_password_token, :reset_password_sent_at, :remember_created_at, :confirmation_token, :confirmed_at, :confirmation_sent_at, :unconfirmed_email, :failed_attempts, :unlock_token, :locked_at
    end
  end
end
