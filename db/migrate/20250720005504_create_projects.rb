class CreateProjects < ActiveRecord::Migration[8.0]
  def change
    create_table :projects, id: :uuid do |t|
      t.string :name, null: false
      t.string :description, null: false
      t.timestamp :start_date, null: false
      t.timestamp :interest_deadline, null: false
      t.integer :interest_count, null: false, default: 0
      t.integer :status, null: false, default: 0

      t.references :property, null: false, foreign_key: true, type: :uuid

      t.timestamps
    end
  end
end
