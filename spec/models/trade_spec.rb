# == Schema Information
#
# Table name: trades
#
#  id         :uuid             not null, primary key
#  name       :string           not null
#  quick_hire :boolean          default(FALSE), not null
#  see_all    :boolean          default(FALSE), not null
#  created_at :datetime         not null
#  updated_at :datetime         not null
#  parent_id  :uuid
#
require "rails_helper"

RSpec.describe Trade, type: :model do
  subject(:trade) { build(:trade) }

  describe "validations" do
    it { is_expected.to validate_presence_of(:name) }
  end

  describe "associations" do
    it { is_expected.to have_many(:contractors) }
    it { is_expected.to have_many(:tasks) }
    it { is_expected.to belong_to(:parent).optional }
    it { is_expected.to have_many(:children).class_name("Trade").with_foreign_key("parent_id") }
  end

  describe "factory" do
    it "is valid" do
      expect(trade).to be_valid
    end
  end

  # Add custom method specs here if any
end
