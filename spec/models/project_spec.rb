# == Schema Information
#
# Table name: projects
#
#  id                :uuid             not null, primary key
#  description       :string           not null
#  interest_count    :integer          default(0), not null
#  interest_deadline :datetime         not null
#  name              :string           not null
#  start_date        :datetime         not null
#  status            :integer          default("draft"), not null
#  created_at        :datetime         not null
#  updated_at        :datetime         not null
#  property_id       :uuid             not null
#
# Indexes
#
#  index_projects_on_property_id  (property_id)
#
# Foreign Keys
#
#  fk_rails_...  (property_id => properties.id)
#
require "rails_helper"

RSpec.describe Project, type: :model do
  subject(:project) { build(:project) }

  describe "validations" do
    it { is_expected.to validate_presence_of(:name) }
    it { is_expected.to validate_presence_of(:description) }
    it { is_expected.to validate_presence_of(:start_date) }
    it { is_expected.to validate_presence_of(:interest_deadline) }
    it { is_expected.to validate_presence_of(:status) }
    it { is_expected.to define_enum_for(:status).with_values(draft: 0, active: 1, awarded: 2, completed: 3, cancelled: 4) }

    it "validates start_date and interest_deadline are on or after today" do
      project.start_date = 1.day.ago
      project.interest_deadline = 1.day.ago
      expect(project).not_to be_valid
      expect(project.errors[:start_date]).to be_present
      expect(project.errors[:interest_deadline]).to be_present
    end

    it "validates start_date is after interest_deadline" do
      project.start_date = 1.day.from_now
      project.interest_deadline = 2.days.from_now
      expect(project).not_to be_valid
      expect(project.errors[:start_date]).to be_present
    end
  end

  describe "associations" do
    it { is_expected.to belong_to(:property) }
    it { is_expected.to have_many(:tasks) }
  end

  describe "factory" do
    it "is valid" do
      expect(project).to be_valid
    end
  end

  # Add custom method specs here if any
end
