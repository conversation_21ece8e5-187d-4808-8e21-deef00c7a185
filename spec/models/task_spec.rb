# == Schema Information
#
# Table name: tasks
#
#  id         :uuid             not null, primary key
#  name       :string
#  created_at :datetime         not null
#  updated_at :datetime         not null
#  project_id :uuid             not null
#  trade_id   :uuid             not null
#
# Indexes
#
#  index_tasks_on_project_id  (project_id)
#  index_tasks_on_trade_id    (trade_id)
#
# Foreign Keys
#
#  fk_rails_...  (project_id => projects.id)
#  fk_rails_...  (trade_id => trades.id)
#
require "rails_helper"

RSpec.describe Task, type: :model do
  subject(:task) { build(:task) }

  describe "validations" do
    it { is_expected.to validate_presence_of(:name) }
  end

  describe "associations" do
    it { is_expected.to belong_to(:project) }
    it { is_expected.to belong_to(:trade) }
  end

  describe "factory" do
    it "is valid" do
      expect(task).to be_valid
    end
  end

  # Add custom method specs here if any
end
