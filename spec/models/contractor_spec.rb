# == Schema Information
#
# Table name: contractors
#
#  id            :uuid             not null, primary key
#  email         :string           not null
#  facebook_url  :string
#  instagram_url :string
#  linkedin_url  :string
#  logo_url      :string
#  name          :string           not null
#  phone         :string           not null
#  quick_hire    :boolean          default(FALSE)
#  website_url   :string
#  x_url         :string
#  created_at    :datetime         not null
#  updated_at    :datetime         not null
#  trade_id      :uuid             not null
#  user_id       :uuid             not null
#
# Indexes
#
#  index_contractors_on_trade_id  (trade_id)
#  index_contractors_on_user_id   (user_id)
#
# Foreign Keys
#
#  fk_rails_...  (trade_id => trades.id)
#  fk_rails_...  (user_id => users.id)
#
require "rails_helper"

RSpec.describe Contractor, type: :model do
  subject(:contractor) { build(:contractor) }

  describe "validations" do
    it { is_expected.to validate_presence_of(:name) }
    it { is_expected.to validate_presence_of(:email) }
    it { is_expected.to validate_presence_of(:phone) }
  end

  describe "associations" do
    it { is_expected.to belong_to(:trade) }
    it { is_expected.to have_one(:address) }
  end

  describe "factory" do
    it "is valid" do
      expect(contractor).to be_valid
    end
  end

  # Add custom method specs here if any
end
