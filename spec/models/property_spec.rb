# == Schema Information
#
# Table name: properties
#
#  id            :uuid             not null, primary key
#  deleted_at    :datetime
#  description   :string
#  name          :string           not null
#  property_type :integer          default("single_family"), not null
#  created_at    :datetime         not null
#  updated_at    :datetime         not null
#  user_id       :uuid             not null
#
# Indexes
#
#  index_properties_on_user_id  (user_id)
#
# Foreign Keys
#
#  fk_rails_...  (user_id => users.id)
#
require "rails_helper"

RSpec.describe Property, type: :model do
  subject(:property) { build(:property) }

  describe "validations" do
    it { is_expected.to validate_presence_of(:name) }
  end

  describe "associations" do
    it { is_expected.to belong_to(:user) }
    it { is_expected.to have_many(:projects) }
    it { is_expected.to have_one(:address) }
  end

  describe "factory" do
    it "is valid" do
      expect(property).to be_valid
    end
  end

  # Add custom method specs here if any
end
