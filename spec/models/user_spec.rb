# == Schema Information
#
# Table name: users
#
#  id                       :uuid             not null, primary key
#  current_sign_in_at       :datetime
#  current_sign_in_ip       :string
#  email                    :string           default(""), not null
#  last_sign_in_at          :datetime
#  last_sign_in_ip          :string
#  name                     :string           not null
#  onboarded                :boolean          default(FALSE), not null
#  otp_required_for_sign_in :boolean          default(FALSE), not null
#  otp_secret               :string           default(""), not null
#  password_digest          :string           default(""), not null
#  provider                 :string
#  role                     :integer          default("homeowner"), not null
#  sign_in_count            :integer          default(0), not null
#  uid                      :string
#  verified                 :boolean          default(FALSE), not null
#  created_at               :datetime         not null
#  updated_at               :datetime         not null
#
# Indexes
#
#  index_users_on_email             (email) UNIQUE
#  index_users_on_provider_and_uid  (provider,uid) UNIQUE
#
require "rails_helper"

RSpec.describe User, type: :model do
  subject(:user) { build(:user) }

  describe "validations" do
    it { is_expected.to validate_presence_of(:name) }
    it { is_expected.to validate_presence_of(:email) }
    it { is_expected.to validate_uniqueness_of(:email).case_insensitive }
    it { is_expected.to validate_presence_of(:password).on(:create) }
    it { is_expected.to validate_length_of(:password).is_at_least(6).on(:create) }
    it { is_expected.to validate_presence_of(:password_confirmation).on(:create) }
    it { is_expected.to validate_length_of(:password_confirmation).is_at_least(6).on(:create) }
    it "validates password confirmation matches password" do
      user.password = "password123"
      user.password_confirmation = "different"
      expect(user).not_to be_valid
      expect(user.errors[:password_confirmation]).to be_present
    end
  end

  describe "associations" do
    it { is_expected.to have_many(:properties) }
  end

  describe "factory" do
    it "is valid" do
      expect(user).to be_valid
    end
  end

  # Add custom method specs here if any
end
