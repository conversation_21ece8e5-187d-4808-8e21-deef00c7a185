# == Schema Information
#
# Table name: ai_agents
#
#  id            :uuid             not null, primary key
#  api_key       :string
#  model_type    :integer          default("gpt_4o"), not null
#  name          :string           default("My AI Assistant"), not null
#  provider      :integer          default("openai"), not null
#  settings      :json
#  system_prompt :text
#  created_at    :datetime         not null
#  updated_at    :datetime         not null
#  user_id       :uuid             not null
#
# Indexes
#
#  index_ai_agents_on_user_id               (user_id)
#  index_ai_agents_on_user_id_and_provider  (user_id,provider)
#
# Foreign Keys
#
#  fk_rails_...  (user_id => users.id)
#
require "rails_helper"

RSpec.describe AiAgent, type: :model do
  let(:user) { create(:user) }

  describe "associations" do
    it { should belong_to(:user) }
  end

  describe "validations" do
    it { should belong_to(:user) }
    it { should validate_presence_of(:provider) }
    it { should validate_presence_of(:model_type) }

    context "when API key is required" do
      before do
        allow(ENV).to receive(:[]).with("OPENAI_API_KEY").and_return(nil)
        allow(ENV).to receive(:[]).with("ANTHROPIC_API_KEY").and_return(nil)
      end

      it { should validate_presence_of(:api_key) }
    end
  end

  describe "factory" do
    it "creates a valid ai_agent" do
      ai_agent = build(:ai_agent, user: user)
      expect(ai_agent).to be_valid
    end
  end

  describe "default values" do
    it "sets default model_type" do
      ai_agent = AiAgent.new(user: user, name: "Test Agent", api_key: "test-key")
      expect(ai_agent.model_type).to eq("gpt_4o")
    end

    it "sets default provider" do
      ai_agent = AiAgent.new(user: user, name: "Test Agent", api_key: "test-key")
      expect(ai_agent.provider).to eq("openai")
    end

    it "sets default name" do
      ai_agent = AiAgent.new(user: user, api_key: "test-key")
      expect(ai_agent.name).to eq("Jack")
    end
  end

  describe "#answer_question" do
    let(:ai_agent) { create(:ai_agent, user: user) }
    let(:question) { "How do I fix a leaky faucet?" }
    let(:context) { {} }

    context "with valid OpenAI response" do
      let(:mock_response) do
        {
          "choices" => [
            {
              "message" => {
                "content" => "To fix a leaky faucet, first turn off the water supply..."
              }
            }
          ]
        }
      end

      before do
        allow_any_instance_of(OpenAI::Client).to receive(:chat).and_return(mock_response)
      end

      it "returns the AI response" do
        response = ai_agent.answer_question(question, context)
        expect(response).to eq("To fix a leaky faucet, first turn off the water supply...")
      end

      it "includes context in the system prompt" do
        property = create(:property, user: user, name: "Main House")
        context_with_property = {property: property}

        response = ai_agent.answer_question(question, context_with_property)
        expect(response).to be_present
      end
    end

    context "when OpenAI API fails" do
      before do
        allow_any_instance_of(OpenAI::Client).to receive(:chat).and_raise(StandardError.new("API Error"))
      end

      it "raises an error" do
        expect { ai_agent.answer_question(question, context) }.to raise_error(StandardError)
      end
    end
  end

  describe "#generate_project_suggestions" do
    let(:ai_agent) { create(:ai_agent, user: user) }
    let(:property) { create(:property, user: user) }
    let(:requirements) { "Kitchen renovation with new cabinets and countertops" }

    context "with valid OpenAI response" do
      let(:mock_response) do
        {
          "choices" => [
            {
              "message" => {
                "content" => "Here is a detailed kitchen renovation plan..."
              }
            }
          ]
        }
      end

      before do
        allow_any_instance_of(OpenAI::Client).to receive(:chat).and_return(mock_response)
      end

      it "returns project suggestions" do
        response = ai_agent.generate_project_suggestions(property, requirements)
        expect(response).to eq("Here is a detailed kitchen renovation plan...")
      end

      it "includes property information in the prompt" do
        response = ai_agent.generate_project_suggestions(property, requirements)
        expect(response).to be_present
      end
    end
  end
end
