# == Schema Information
#
# Table name: addresses
#
#  id               :uuid             not null, primary key
#  addressable_type :string
#  city             :string
#  latitude         :decimal(, )
#  longitude        :decimal(, )
#  state            :string
#  street           :string
#  zip_code         :string
#  created_at       :datetime         not null
#  updated_at       :datetime         not null
#  addressable_id   :uuid
#
# Indexes
#
#  index_addresses_on_addressable             (addressable_type,addressable_id)
#  index_addresses_on_latitude_and_longitude  (latitude,longitude)
#
require "rails_helper"

RSpec.describe Address, type: :model do
  subject(:address) { build(:address) }

  describe "validations" do
    it { is_expected.to validate_presence_of(:street) }
    it { is_expected.to validate_presence_of(:city) }
    it { is_expected.to validate_presence_of(:state) }
    it { is_expected.to validate_presence_of(:zip_code) }
  end

  describe "associations" do
    it { is_expected.to belong_to(:addressable) }
  end

  describe "factory" do
    it "is valid" do
      expect(address).to be_valid
    end
  end

  # Add custom method specs here if any
end
