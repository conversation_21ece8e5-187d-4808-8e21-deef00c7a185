require "rails_helper"

RSpec.describe AiService do
  let(:property) { create(:property, name: "Main House", property_type: "house") }

  describe ".ask_question" do
    let(:question) { "How do I fix a leaky faucet?" }
    let(:context) { {} }

    context "in development without API key" do
      before do
        allow(Rails).to receive(:env).and_return(ActiveSupport::StringInquirer.new("development"))
        stub_const("AiService::API_KEY", nil)
      end

      it "returns a mock response" do
        response = AiService.ask_question(question, context)
        expect(response).to include("Hi there! I'm Jack")
        expect(response).to include("OpenAI API key")
      end
    end

    context "with API key configured" do
      let(:mock_response) do
        {
          "choices" => [
            {
              "message" => {
                "content" => "To fix a leaky faucet, first turn off the water supply..."
              }
            }
          ]
        }
      end

      before do
        stub_const("AiService::API_KEY", "test-api-key")
        allow_any_instance_of(AiService).to receive(:make_api_call).and_return(mock_response)
      end

      it "returns the AI response" do
        response = AiService.ask_question(question, context)
        expect(response).to eq("To fix a leaky faucet, first turn off the water supply...")
      end

      it "includes property context in the prompt" do
        context_with_property = {property: property}
        service = AiService.new

        expect(service).to receive(:build_general_system_prompt).with(context_with_property).and_call_original
        service.ask_question(question, context_with_property)
      end
    end

    context "when API call fails" do
      before do
        stub_const("AiService::API_KEY", "test-api-key")
        allow_any_instance_of(AiService).to receive(:make_api_call).and_raise(StandardError.new("API Error"))
      end

      it "raises an error" do
        expect { AiService.ask_question(question, context) }.to raise_error(StandardError, "API Error")
      end
    end
  end

  describe ".conversational_project_planning" do
    let(:message) { "I want to renovate my kitchen" }
    let(:conversation_state) { {"stage" => "initial"} }
    let(:stage) { "initial" }

    let(:mock_response) do
      {
        "choices" => [
          {
            "message" => {
              "content" => "That sounds like an exciting project! Tell me more about your kitchen..."
            }
          }
        ]
      }
    end

    context "with valid parameters" do
      before do
        stub_const("AiService::API_KEY", "test-api-key")
        allow_any_instance_of(AiService).to receive(:make_api_call).and_return(mock_response)
      end

      it "returns a structured response" do
        response = AiService.conversational_project_planning(
          message: message,
          property: property,
          conversation_state: conversation_state,
          stage: stage
        )

        expect(response[:success]).to be true
        expect(response[:response]).to include("That sounds like an exciting project!")
        expect(response[:conversation_state]).to be_present
      end

      it "updates conversation history" do
        response = AiService.conversational_project_planning(
          message: message,
          property: property,
          conversation_state: conversation_state,
          stage: stage
        )

        messages = response[:conversation_state]["messages"]
        expect(messages).to be_present
        expect(messages.last["role"]).to eq("assistant")
        expect(messages.last["content"]).to include("That sounds like an exciting project!")
      end
    end

    context "when AI indicates ready to create project" do
      let(:mock_response_with_command) do
        {
          "choices" => [
            {
              "message" => {
                "content" => "READY_TO_CREATE_PROJECT Based on our conversation, I can create a detailed kitchen renovation plan..."
              }
            }
          ]
        }
      end

      before do
        stub_const("AiService::API_KEY", "test-api-key")
        allow_any_instance_of(AiService).to receive(:make_api_call).and_return(mock_response_with_command)
      end

      it "returns show_project_summary action" do
        response = AiService.conversational_project_planning(
          message: message,
          property: property,
          conversation_state: conversation_state,
          stage: stage
        )

        expect(response[:action]).to eq("show_project_summary")
        expect(response[:data]).to be_present
        expect(response[:conversation_state]["stage"]).to eq("creating_project")
      end
    end

    context "in development without API key" do
      before do
        allow(Rails).to receive(:env).and_return(ActiveSupport::StringInquirer.new("development"))
        stub_const("AiService::API_KEY", nil)
      end

      it "returns a mock response" do
        response = AiService.conversational_project_planning(
          message: message,
          property: property,
          conversation_state: conversation_state,
          stage: stage
        )

        expect(response[:success]).to be true
        expect(response[:response]).to include("Hi there! I'm Jack")
      end
    end
  end

  describe "#build_general_system_prompt" do
    let(:service) { AiService.new }

    it "includes Jack's personality" do
      prompt = service.send(:build_general_system_prompt, {})
      expect(prompt).to include("You are Jack")
      expect(prompt).to include("30 years")
      expect(prompt).to include("home improvement")
    end

    it "includes property context when provided" do
      context = {property: property}
      prompt = service.send(:build_general_system_prompt, context)
      expect(prompt).to include("Main House")
      expect(prompt).to include("house property")
    end

    it "includes project context when provided" do
      project = create(:project, name: "Kitchen Renovation")
      context = {project: project}
      prompt = service.send(:build_general_system_prompt, context)
      expect(prompt).to include("Kitchen Renovation")
    end
  end

  describe "#build_conversational_project_prompt" do
    let(:service) { AiService.new }
    let(:conversation_state) { {"stage" => "initial"} }
    let(:stage) { "initial" }

    it "includes conversational guidelines" do
      prompt = service.send(:build_conversational_project_prompt, property, conversation_state, stage)
      expect(prompt).to include("You are Jack")
      expect(prompt).to include("conversation with a homeowner")
      expect(prompt).to include("initial: Get basic project idea")
    end

    it "includes property information" do
      prompt = service.send(:build_conversational_project_prompt, property, conversation_state, stage)
      expect(prompt).to include("house - Main House")
    end

    it "includes existing project data" do
      conversation_state["project_data"] = {"type" => "kitchen renovation"}
      prompt = service.send(:build_conversational_project_prompt, property, conversation_state, stage)
      expect(prompt).to include("kitchen renovation")
    end
  end

  describe "#parse_conversational_response" do
    let(:service) { AiService.new }
    let(:conversation_state) { {"stage" => "initial"} }
    let(:stage) { "initial" }

    it "parses READY_TO_CREATE_PROJECT command" do
      response = "READY_TO_CREATE_PROJECT I have enough information to create your project plan."
      result = service.send(:parse_conversational_response, response, conversation_state, stage)

      expect(result[:action]).to eq("show_project_summary")
      expect(result[:state_updates]["stage"]).to eq("creating_project")
      expect(result[:response]).not_to include("READY_TO_CREATE_PROJECT")
    end

    it "updates stage based on conversation progress" do
      response = "Tell me more about your project requirements."
      result = service.send(:parse_conversational_response, response, conversation_state, stage)

      expect(result[:state_updates]["stage"]).to eq("gathering_requirements")
    end

    it "returns clean response without commands" do
      response = "This is a normal response without commands."
      result = service.send(:parse_conversational_response, response, conversation_state, stage)

      expect(result[:response]).to eq(response)
      expect(result[:action]).to be_nil
    end
  end
end
