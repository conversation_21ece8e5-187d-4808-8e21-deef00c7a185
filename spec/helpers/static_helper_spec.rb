require "rails_helper"

# Specs in this file have access to a helper object that includes
# the StaticHelper. For example:
#
# describe StaticHelper do
#   describe "string concat" do
#     it "concats two strings with spaces" do
#       expect(helper.concat_strings("this","that")).to eq("this that")
#     end
#   end
# end
RSpec.describe StaticHelper, type: :helper do
  # Since StaticHelper is currently empty, we'll test that it exists and can be included
  describe "module inclusion" do
    it "can be included without errors" do
      expect { helper.class.include(StaticHelper) }.not_to raise_error
    end
  end

  # Test that the helper module is properly defined
  describe "module definition" do
    it "is defined as a module" do
      expect(StaticHelper).to be_a(Module)
    end
  end
end
