require "rails_helper"

RSpec.describe "AI Workflow Integration", type: :system do
  let(:user) { create(:user) }
  let(:session_record) { create(:session, user: user) }
  let(:property) { create(:property, user: user, name: "Main House") }

  before do
    page.driver.browser.set_cookie("session_token", session_record.id)

    # Mock AI responses for different stages
    allow(AiService).to receive(:ask_question).and_return("Here is helpful advice about your home project.")
    allow(AiService).to receive(:conversational_project_planning).and_return({
      success: true,
      response: "Great! Let me help you plan this kitchen renovation.",
      conversation_state: {stage: "gathering_requirements"},
      action: nil,
      data: nil
    })
  end

  describe "Complete AI Workflow" do
    it "allows user to go from general question to project planning", js: true do
      # Start on dashboard
      visit dashboard_path

      # Use FAB for quick question
      find('button[data-action="click->ai-chat#toggle"]').click

      within('[data-ai-chat-target="interface"]') do
        fill_in "Ask Jack anything...", with: "What should I consider for a kitchen renovation?"
        click_button "Send"
      end

      expect(page).to have_text("Here is helpful advice about your home project.")

      # Navigate to project planning
      within('[data-ai-chat-target="interface"]') do
        click_link "Plan Project"
      end

      expect(current_path).to eq(project_ai_path)
      expect(page).to have_text("AI Project Planner")
    end

    it "supports full conversational project creation flow", js: true do
      visit project_ai_path

      # Start conversation
      fill_in "Tell Jack about your project idea...", with: "I want to renovate my kitchen"
      click_button "Send"

      expect(page).to have_text("I want to renovate my kitchen")
      expect(page).to have_text("Great! Let me help you plan this kitchen renovation.")

      # Continue conversation with more details
      allow(AiService).to receive(:conversational_project_planning).and_return({
        success: true,
        response: "Perfect! Based on our discussion, I can create a detailed project plan.",
        conversation_state: {stage: "creating_project"},
        action: "show_project_summary",
        data: {
          name: "Kitchen Renovation",
          description: "Complete kitchen makeover with new cabinets and countertops",
          duration: "2-3 weeks",
          complexity: "Medium"
        }
      })

      fill_in "Tell Jack about your project idea...", with: "I want new cabinets, granite countertops, and stainless appliances"
      click_button "Send"

      expect(page).to have_text("📋 Project Summary")
      expect(page).to have_text("Kitchen Renovation")
      expect(page).to have_button("✅ Looks Good - Create Project")
    end
  end

  describe "Cross-Feature Navigation" do
    it "maintains context when switching between AI features", js: true do
      # Start with general chat
      visit ai_chat_path

      fill_in "Ask your question...", with: "Kitchen renovation tips"
      select property.name, from: "Select property..."
      click_button "Send"

      expect(AiService).to have_received(:ask_question).with(
        "Kitchen renovation tips",
        hash_including(property: property)
      )

      # Navigate to project AI
      click_link "Back to Dashboard"
      click_link "AI Project Creator"

      expect(current_path).to eq(project_ai_path)
      # Property context should be maintained or easily selectable
    end
  end

  describe "Error Recovery" do
    it "handles AI service failures gracefully", js: true do
      allow(AiService).to receive(:ask_question).and_raise(StandardError.new("API Error"))

      visit ai_chat_path

      fill_in "Ask your question...", with: "Test question"
      click_button "Send"

      expect(page).to have_text("Sorry, I encountered an error")

      # User should be able to try again
      allow(AiService).to receive(:ask_question).and_return("This works now!")

      fill_in "Ask your question...", with: "Another question"
      click_button "Send"

      expect(page).to have_text("This works now!")
    end

    it "handles network failures in project AI", js: true do
      visit project_ai_path

      # Simulate network failure
      allow(AiService).to receive(:conversational_project_planning).and_return({
        success: false,
        response: "I'm having trouble right now. Please try again.",
        error: "Network error"
      })

      fill_in "Tell Jack about your project idea...", with: "Kitchen renovation"
      click_button "Send"

      expect(page).to have_text("I'm having trouble right now")

      # User should be able to retry
      expect(page).to have_field("Tell Jack about your project idea...")
    end
  end

  describe "Multi-Property Workflow" do
    let!(:second_property) { create(:property, user: user, name: "Rental Property") }

    it "handles multiple properties correctly", js: true do
      visit project_ai_path

      # Should show property selection
      expect(page).to have_select("Which property is this project for?")

      # Select specific property
      select "Rental Property", from: "Which property is this project for?"

      fill_in "Tell Jack about your project idea...", with: "Bathroom renovation"
      click_button "Send"

      expect(AiService).to have_received(:conversational_project_planning).with(
        hash_including(property: second_property)
      )
    end
  end

  describe "Session Persistence" do
    it "maintains conversation state across page reloads", js: true do
      visit project_ai_path

      # Start conversation
      fill_in "Tell Jack about your project idea...", with: "Kitchen renovation"
      click_button "Send"

      expect(page).to have_text("Kitchen renovation")

      # Reload page (conversation history would be lost in current implementation)
      visit current_path

      # Should still be able to start new conversation
      expect(page).to have_field("Tell Jack about your project idea...")
      expect(page).to have_text("What kind of project are you thinking about?")
    end
  end

  describe "Performance and Responsiveness" do
    it "shows loading states during AI processing", js: true do
      # Simulate slow AI response
      allow(AiService).to receive(:ask_question) do
        sleep(0.1) # Brief delay to see loading state
        "Response after delay"
      end

      visit ai_chat_path

      fill_in "Ask your question...", with: "Test question"
      click_button "Send"

      # Should show typing indicator
      expect(page).to have_text("Jack is thinking...", wait: 0.5)

      # Then show actual response
      expect(page).to have_text("Response after delay", wait: 2)
    end
  end
end
