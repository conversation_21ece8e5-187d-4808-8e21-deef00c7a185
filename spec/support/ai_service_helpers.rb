module AiServiceHelpers
  def mock_ai_chat_response(response_text = "This is a helpful AI response.")
    allow(AiService).to receive(:ask_question).and_return(response_text)
  end

  def mock_ai_project_response(response_data = {})
    default_response = {
      success: true,
      response: "Great project idea! Tell me more about your requirements.",
      conversation_state: {stage: "gathering_requirements"},
      action: nil,
      data: nil
    }

    allow(AiService).to receive(:conversational_project_planning)
      .and_return(default_response.merge(response_data))
  end

  def mock_ai_project_creation_response(project_data = {})
    default_data = {
      name: "AI Generated Project",
      description: "Project created through AI conversation",
      duration: "1-2 weeks",
      complexity: "Medium"
    }

    mock_ai_project_response(
      response: "Perfect! I have enough information to create your project.",
      action: "show_project_summary",
      data: default_data.merge(project_data),
      conversation_state: {stage: "creating_project"}
    )
  end

  def mock_ai_error
    allow(AiService).to receive(:ask_question).and_raise(StandardError.new("API Error"))
    allow(AiService).to receive(:conversational_project_planning).and_raise(StandardError.new("API Error"))
  end

  def mock_ai_service_unavailable
    allow(AiService).to receive(:ask_question).and_return("I'm currently unavailable. Please try again later.")
    allow(AiService).to receive(:conversational_project_planning).and_return({
      success: false,
      response: "I'm currently unavailable. Please try again later.",
      error: "Service unavailable"
    })
  end
end

RSpec.configure do |config|
  config.include AiServiceHelpers, type: :system
  config.include AiServiceHelpers, type: :controller
  config.include AiServiceHelpers, type: :request
end
