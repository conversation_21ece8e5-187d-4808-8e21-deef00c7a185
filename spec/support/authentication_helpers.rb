module AuthenticationHelpers
  def sign_in_as(user)
    session_record = create(:session, user: user)
    if respond_to?(:page) # System tests
      page.driver.browser.set_cookie("session_token", session_record.id)
    else # Request tests
      # For request tests, we need to set cookies directly
      cookies.signed[:session_token] = session_record.id
    end
  end

  def sign_out
    if respond_to?(:page) # System tests
      page.driver.browser.clear_cookies
    else # Request tests
      cookies.signed[:session_token] = nil
    end
  end

  def create_authenticated_user
    user = create(:user)
    sign_in_as(user)
    user
  end
end

RSpec.configure do |config|
  config.include AuthenticationHelpers, type: :system
  config.include AuthenticationHelpers, type: :request
end
