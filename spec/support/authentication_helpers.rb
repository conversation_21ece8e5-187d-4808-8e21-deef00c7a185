module AuthenticationHel<PERSON>
  def sign_in_as(user)
    session_record = create(:session, user: user)
    if respond_to?(:page) # System tests
      # For system tests, we need to visit a page first to establish domain
      page.visit("/")

      # Now set the cookie with proper domain
      case page.driver
      when Capybara::Selenium::Driver
        # Selenium WebDriver (Chrome, Firefox, etc.)
        page.driver.browser.manage.add_cookie(name: "session_token", value: session_record.id)
      when Capybara::RackTest::Driver
        # Rack::Test driver - set cookie via page driver
        page.driver.browser.set_cookie("session_token=#{session_record.id}")
      else
        # Fallback - try to set cookie directly
        begin
          page.driver.browser.set_cookie("session_token", session_record.id)
        rescue
          # If all else fails, visit a page with the cookie set
          page.visit("/?session_token=#{session_record.id}")
        end
      end
    else # Request tests
      # For request tests, we'll use a more direct approach
      # Store the session record for use in controller mocking
      @current_test_session = session_record
      @current_test_user = user

      # Mock the authenticate method to bypass authentication
      allow_any_instance_of(ApplicationController).to receive(:authenticate) do |controller|
        Current.session = @current_test_session
      end

      # Mock the set_current_user method
      allow_any_instance_of(ApplicationController).to receive(:set_current_user) do |controller|
        Current.session = @current_test_session
      end
    end
  end

  def sign_out
    if respond_to?(:page) # System tests
      case page.driver
      when Capybara::Selenium::Driver
        page.driver.browser.manage.delete_all_cookies
      when Capybara::RackTest::Driver
        page.driver.browser.clear_cookies
      else
        begin
          page.driver.browser.clear_cookies
        rescue
          # Fallback - visit a page that will clear the session
          page.visit("/sign_out")
        end
      end
    else # Request tests
      @current_test_session = nil
      @current_test_user = nil
      Current.session = nil

      # Mock authenticate to redirect to sign in
      allow_any_instance_of(ApplicationController).to receive(:authenticate) do |controller|
        controller.redirect_to controller.sign_in_path
      end

      # Mock set_current_user to do nothing
      allow_any_instance_of(ApplicationController).to receive(:set_current_user) do |controller|
        Current.session = nil
      end
    end
  end

  def create_authenticated_user
    user = create(:user)
    sign_in_as(user)
    user
  end
end

RSpec.configure do |config|
  config.include AuthenticationHelpers, type: :system
  config.include AuthenticationHelpers, type: :request
end
