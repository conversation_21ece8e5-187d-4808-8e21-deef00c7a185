module AuthenticationHelpers
  def sign_in_as(user)
    session_record = create(:session, user: user)
    if respond_to?(:page) # System tests
      # For system tests, we need to set the cookie in the browser
      case page.driver
      when Capybara::Selenium::Driver
        # Selenium WebDriver (Chrome, Firefox, etc.)
        page.driver.browser.manage.add_cookie(name: "session_token", value: session_record.id)
      when Capybara::RackTest::Driver
        # Rack::Test driver - set cookie via page driver
        page.driver.browser.set_cookie("session_token=#{session_record.id}")
      else
        # Fallback - try to set cookie directly
        begin
          page.driver.browser.set_cookie("session_token", session_record.id)
        rescue
          # If all else fails, visit a page with the cookie set
          page.visit("/?session_token=#{session_record.id}")
        end
      end
    else # Request tests
      # For request tests, we need to set the signed cookie
      # We'll mock the signed cookies to return our session ID
      allow_any_instance_of(ActionDispatch::Cookies::SignedKeyRotatingCookieJar).to receive(:[]).with(:session_token).and_return(session_record.id)
      # Also set Current.session directly for immediate access
      Current.session = session_record
    end
  end

  def sign_out
    if respond_to?(:page) # System tests
      case page.driver
      when Capybara::Selenium::Driver
        page.driver.browser.manage.delete_all_cookies
      when Capybara::RackTest::Driver
        page.driver.browser.clear_cookies
      else
        begin
          page.driver.browser.clear_cookies
        rescue
          # Fallback - visit a page that will clear the session
          page.visit("/sign_out")
        end
      end
    else # Request tests
      allow_any_instance_of(ActionDispatch::Cookies::SignedKeyRotatingCookieJar).to receive(:[]).with(:session_token).and_return(nil)
      Current.session = nil
    end
  end

  def create_authenticated_user
    user = create(:user)
    sign_in_as(user)
    user
  end
end

RSpec.configure do |config|
  config.include AuthenticationHelpers, type: :system
  config.include AuthenticationHelpers, type: :request
end
