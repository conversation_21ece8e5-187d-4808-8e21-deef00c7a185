# == Schema Information
#
# Table name: properties
#
#  id            :uuid             not null, primary key
#  deleted_at    :datetime
#  description   :string
#  name          :string           not null
#  property_type :integer          default("single_family"), not null
#  created_at    :datetime         not null
#  updated_at    :datetime         not null
#  user_id       :uuid             not null
#
# Indexes
#
#  index_properties_on_user_id  (user_id)
#
# Foreign Keys
#
#  fk_rails_...  (user_id => users.id)
#
FactoryBot.define do
  factory :property do
    name { "Sample Property" }
    description { "A beautiful property." }
    property_type { :single_family }
    user

    after(:build) do |property|
      property.image.attach(
        io: StringIO.new("fake image data"),
        filename: "test_image.png",
        content_type: "image/png"
      )
    end
  end
end
