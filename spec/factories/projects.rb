# == Schema Information
#
# Table name: projects
#
#  id                :uuid             not null, primary key
#  description       :string           not null
#  interest_count    :integer          default(0), not null
#  interest_deadline :datetime         not null
#  name              :string           not null
#  start_date        :datetime         not null
#  status            :integer          default("draft"), not null
#  created_at        :datetime         not null
#  updated_at        :datetime         not null
#  property_id       :uuid             not null
#
# Indexes
#
#  index_projects_on_property_id  (property_id)
#
# Foreign Keys
#
#  fk_rails_...  (property_id => properties.id)
#
FactoryBot.define do
  factory :project do
    name { "New Build" }
    description { "A new residential build." }
    interest_deadline { 1.week.from_now }
    start_date { 2.weeks.from_now }
    property
  end
end
