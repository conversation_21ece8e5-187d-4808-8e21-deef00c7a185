# == Schema Information
#
# Table name: addresses
#
#  id               :uuid             not null, primary key
#  addressable_type :string
#  city             :string
#  latitude         :decimal(, )
#  longitude        :decimal(, )
#  state            :string
#  street           :string
#  zip_code         :string
#  created_at       :datetime         not null
#  updated_at       :datetime         not null
#  addressable_id   :uuid
#
# Indexes
#
#  index_addresses_on_addressable             (addressable_type,addressable_id)
#  index_addresses_on_latitude_and_longitude  (latitude,longitude)
#
FactoryBot.define do
  factory :address do
    street { "123 Main St" }
    city { "Winnipeg" }
    state { "MB" }
    zip_code { "R3C 1A1" }

    association :addressable, factory: :property

    trait :for_property do
      association :addressable, factory: :property
    end

    trait :for_contractor do
      association :addressable, factory: :contractor
    end
  end
end
