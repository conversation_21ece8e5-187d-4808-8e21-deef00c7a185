# == Schema Information
#
# Table name: tasks
#
#  id         :uuid             not null, primary key
#  name       :string
#  created_at :datetime         not null
#  updated_at :datetime         not null
#  project_id :uuid             not null
#  trade_id   :uuid             not null
#
# Indexes
#
#  index_tasks_on_project_id  (project_id)
#  index_tasks_on_trade_id    (trade_id)
#
# Foreign Keys
#
#  fk_rails_...  (project_id => projects.id)
#  fk_rails_...  (trade_id => trades.id)
#
FactoryBot.define do
  factory :task do
    name { "Framing" }
    project
    trade
  end
end
