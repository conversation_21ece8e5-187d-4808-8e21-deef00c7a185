# == Schema Information
#
# Table name: trades
#
#  id         :uuid             not null, primary key
#  name       :string           not null
#  quick_hire :boolean          default(FALSE), not null
#  see_all    :boolean          default(FALSE), not null
#  created_at :datetime         not null
#  updated_at :datetime         not null
#  parent_id  :uuid
#
FactoryBot.define do
  factory :trade do
    name { "Carpentry" }
    quick_hire { false }
    see_all { false }
  end
end
