# == Schema Information
#
# Table name: users
#
#  id                       :uuid             not null, primary key
#  current_sign_in_at       :datetime
#  current_sign_in_ip       :string
#  email                    :string           default(""), not null
#  last_sign_in_at          :datetime
#  last_sign_in_ip          :string
#  name                     :string           not null
#  onboarded                :boolean          default(FALSE), not null
#  otp_required_for_sign_in :boolean          default(FALSE), not null
#  otp_secret               :string           default(""), not null
#  password_digest          :string           default(""), not null
#  provider                 :string
#  role                     :integer          default("homeowner"), not null
#  sign_in_count            :integer          default(0), not null
#  uid                      :string
#  verified                 :boolean          default(FALSE), not null
#  created_at               :datetime         not null
#  updated_at               :datetime         not null
#
# Indexes
#
#  index_users_on_email             (email) UNIQUE
#  index_users_on_provider_and_uid  (provider,uid) UNIQUE
#
FactoryBot.define do
  factory :user do
    name { Faker::Name.name }
    email { Faker::Internet.unique.email }
    password { "SecureP@ssw0rd!2024#{SecureRandom.hex(4)}" }
    password_confirmation { password }
  end
end
