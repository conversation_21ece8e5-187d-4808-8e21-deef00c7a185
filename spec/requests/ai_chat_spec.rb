require "rails_helper"

RSpec.describe "AiChat", type: :request do
  let(:user) { create(:user) }
  let(:property) { create(:property, user: user) }

  before do
    sign_in_user(user)
  end

  describe "GET /ai_chat" do
    it "renders the AI chat interface" do
      get ai_chat_path
      expect(response).to have_http_status(:success)
    end

    it "requires authentication" do
      sign_out_user
      get ai_chat_path
      expect(response).to redirect_to(sign_in_path)
    end
  end

  describe "POST /ai_chat/ask" do
    let(:valid_params) do
      {
        question: "How do I fix a leaky faucet?",
        format: :json
      }
    end

    context "with valid parameters" do
      before do
        allow(AiService).to receive(:ask_question).and_return("Here is how to fix a leaky faucet...")
      end

      it "returns a successful response" do
        post ai_chat_ask_path, params: valid_params
        expect(response).to have_http_status(:success)

        json_response = JSON.parse(response.body)
        expect(json_response["success"]).to be true
        expect(json_response["response"]).to eq("Here is how to fix a leaky faucet...")
      end

      it "calls AiService with the question" do
        expect(AiService).to receive(:ask_question).with("How do I fix a leaky faucet?", {})
        post ai_chat_ask_path, params: valid_params
      end
    end

    context "with property context" do
      let(:params_with_property) do
        valid_params.merge(property_id: property.id)
      end

      before do
        allow(AiService).to receive(:ask_question).and_return("Property-specific advice...")
      end

      it "includes property context" do
        expect(AiService).to receive(:ask_question).with(
          "How do I fix a leaky faucet?",
          {property: property}
        )
        post ai_chat_ask_path, params: params_with_property
      end
    end

    context "when AiService raises an error" do
      before do
        allow(AiService).to receive(:ask_question).and_raise(StandardError.new("API Error"))
      end

      it "returns an error response" do
        post ai_chat_ask_path, params: valid_params
        expect(response).to have_http_status(:success)

        json_response = JSON.parse(response.body)
        expect(json_response["success"]).to be false
        expect(json_response["error"]).to eq("Failed to get response. Please try again.")
      end
    end

    it "requires authentication" do
      sign_out_user
      post ask_ai_chat_path, params: valid_params
      expect(response).to redirect_to(sign_in_path)
    end

    it "requires a question parameter" do
      post ask_ai_chat_path, params: {format: :json}
      expect(response).to have_http_status(:success)

      json_response = JSON.parse(response.body)
      expect(json_response["success"]).to be false
    end
  end

  private

  def sign_in_user(user)
    sign_in_as(user)
  end

  def sign_out_user
    sign_out
  end
end
