require "rails_helper"

RSpec.describe "Sessions", type: :request do
  let(:user) { create(:user, email: "<EMAIL>", password: "Secret1*3*5*", password_confirmation: "Secret1*3*5*") }

  describe "GET /sessions" do
    it "returns http success when authenticated" do
      sign_in_user(user)
      get sessions_path
      expect(response).to have_http_status(:success)
    end
  end

  describe "GET /sign_in" do
    it "returns http success" do
      get sign_in_path
      expect(response).to have_http_status(:success)
    end
  end

  describe "POST /sign_in" do
    context "with valid credentials" do
      it "signs in user and redirects to root" do
        post sign_in_path, params: {email: user.email, password: "Secret1*3*5*"}
        expect(response).to redirect_to(root_path)

        follow_redirect!
        expect(response).to have_http_status(:success)
      end
    end

    context "with invalid credentials" do
      it "redirects back to sign in with error message" do
        post sign_in_path, params: {email: user.email, password: "WrongPassword"}
        expect(response).to redirect_to(sign_in_path(email_hint: user.email))
        expect(flash[:alert]).to eq("That email or password is incorrect")

        get root_path
        expect(response).to redirect_to(sign_in_path)
      end
    end
  end

  describe "DELETE /sessions/:id" do
    it "signs out user and redirects" do
      sign_in_user(user)
      session_record = user.sessions.last

      delete session_path(session_record)
      expect(response).to redirect_to(sessions_path)

      follow_redirect!
      expect(response).to redirect_to(sign_in_path)
    end
  end

  private

  def sign_in_user(user)
    sign_in_as(user)
  end
end
