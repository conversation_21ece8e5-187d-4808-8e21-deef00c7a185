require "rails_helper"

RSpec.describe "ProjectAi", type: :request do
  let(:user) { create(:user) }
  let(:property) { create(:property, user: user) }

  before do
    sign_in_user(user)
  end

  describe "GET /project_ai" do
    it "renders the project AI interface" do
      get project_ai_path
      expect(response).to have_http_status(:success)
    end

    it "requires authentication" do
      sign_out_user
      get project_ai_path
      expect(response).to redirect_to(sign_in_path)
    end
  end

  describe "POST /project_ai/generate_project" do
    let(:valid_params) do
      {
        message: "I want to renovate my kitchen",
        property_id: property.id,
        conversation_state: {stage: "initial"},
        stage: "initial",
        format: :json
      }
    end

    let(:ai_response) do
      {
        success: true,
        response: "Tell me more about your kitchen renovation goals...",
        conversation_state: {stage: "gathering_requirements"},
        action: nil,
        data: nil
      }
    end

    context "with valid parameters" do
      before do
        allow(AiService).to receive(:conversational_project_planning).and_return(ai_response)
      end

      it "returns a successful response" do
        post generate_project_project_ai_path, params: valid_params
        expect(response).to have_http_status(:success)

        json_response = JSON.parse(response.body)
        expect(json_response["success"]).to be true
        expect(json_response["response"]).to eq("Tell me more about your kitchen renovation goals...")
      end

      it "calls AiService with correct parameters" do
        expect(AiService).to receive(:conversational_project_planning).with(
          message: "I want to renovate my kitchen",
          property: property,
          conversation_state: {"stage" => "initial"},
          stage: "initial"
        )
        post generate_project_project_ai_path, params: valid_params
      end
    end

    context "without property_id" do
      let(:params_without_property) do
        valid_params.except(:property_id)
      end

      before do
        allow(AiService).to receive(:conversational_project_planning).and_return(ai_response)
      end

      it "works without property context" do
        expect(AiService).to receive(:conversational_project_planning).with(
          message: "I want to renovate my kitchen",
          property: nil,
          conversation_state: {"stage" => "initial"},
          stage: "initial"
        )
        post generate_project_project_ai_path, params: params_without_property
      end
    end

    context "when AiService raises an error" do
      before do
        allow(AiService).to receive(:conversational_project_planning).and_raise(StandardError.new("API Error"))
      end

      it "returns an error response" do
        post generate_project_project_ai_path, params: valid_params
        expect(response).to have_http_status(:success)

        json_response = JSON.parse(response.body)
        expect(json_response["success"]).to be false
        expect(json_response["error"]).to eq("Failed to process conversation.")
      end
    end

    it "requires authentication" do
      sign_out_user
      post generate_project_project_ai_path, params: valid_params
      expect(response).to redirect_to(sign_in_path)
    end
  end

  private

  def sign_in_user(user)
    sign_in_as(user)
  end

  def sign_out_user
    sign_out
  end
end
