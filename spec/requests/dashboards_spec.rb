require 'rails_helper'

RSpec.describe "Dashboards", type: :request do
  let(:homeowner) { create(:user, role: :homeowner) }
  let(:contractor) { create(:user, role: :contractor) }
  let(:homeowner_session) { create(:session, user: homeowner) }
  let(:contractor_session) { create(:session, user: contractor) }

  describe "GET /dashboard" do
    context "when user is a homeowner" do
      it "redirects to homeowner dashboard" do
        # Mock authentication
        allow_any_instance_of(ApplicationController).to receive(:authenticate).and_return(true)
        allow(Current).to receive(:user).and_return(homeowner)
        
        get "/dashboard"
        expect(response).to redirect_to(homeowner_dashboard_path)
      end
    end

    context "when user is a contractor" do
      it "redirects to contractor dashboard" do
        # Mock authentication
        allow_any_instance_of(ApplicationController).to receive(:authenticate).and_return(true)
        allow(Current).to receive(:user).and_return(contractor)
        
        get "/dashboard"
        expect(response).to redirect_to(contractor_dashboard_path)
      end
    end
  end

  describe "GET /dashboard/homeowner" do
    before do
      allow_any_instance_of(ApplicationController).to receive(:authenticate).and_return(true)
      allow(Current).to receive(:user).and_return(homeowner)
    end

    it "returns http success for homeowners" do
      get homeowner_dashboard_path
      expect(response).to have_http_status(:success)
    end

    it "displays homeowner dashboard content" do
      get homeowner_dashboard_path
      expect(response.body).to include("Homeowner Dashboard")
      expect(response.body).to include("Total Projects")
    end
  end

  describe "GET /dashboard/contractor" do
    before do
      allow_any_instance_of(ApplicationController).to receive(:authenticate).and_return(true)
      allow(Current).to receive(:user).and_return(contractor)
    end

    it "returns http success for contractors" do
      get contractor_dashboard_path
      expect(response).to have_http_status(:success)
    end

    it "displays contractor dashboard content" do
      get contractor_dashboard_path
      expect(response.body).to include("Contractor Dashboard")
      expect(response.body).to include("Available Projects")
    end
  end
end