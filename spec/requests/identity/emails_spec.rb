require "rails_helper"

RSpec.describe "Identity::Emails", type: :request do
  let(:user) { create(:user, password: "Secret1*3*5*", password_confirmation: "Secret1*3*5*") }

  before do
    sign_in_user(user)
  end

  describe "GET /identity/email/edit" do
    it "returns http success" do
      get edit_identity_email_path
      expect(response).to have_http_status(:success)
    end
  end

  describe "PATCH /identity/email" do
    context "with valid password challenge" do
      it "updates email and redirects to root" do
        patch identity_email_path, params: {
          email: "<EMAIL>",
          password_challenge: "Secret1*3*5*"
        }
        expect(response).to redirect_to(root_path)
      end
    end

    context "with invalid password challenge" do
      it "returns unprocessable entity with error message" do
        patch identity_email_path, params: {
          email: "<EMAIL>",
          password_challenge: "WrongPassword"
        }

        expect(response).to have_http_status(:unprocessable_entity)
        expect(response.body).to include("Password challenge is invalid")
      end
    end
  end

  private

  def sign_in_user(user)
    sign_in_as(user)
  end
end
