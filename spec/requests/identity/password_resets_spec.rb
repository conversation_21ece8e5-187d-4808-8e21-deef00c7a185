require "rails_helper"

RSpec.describe "Identity::PasswordResets", type: :request do
  let(:user) { create(:user, verified: true) }

  describe "GET /identity/password_reset/new" do
    it "returns http success" do
      get new_identity_password_reset_path
      expect(response).to have_http_status(:success)
    end
  end

  describe "GET /identity/password_reset/edit" do
    it "returns http success with valid token" do
      sid = user.generate_token_for(:password_reset)

      get edit_identity_password_reset_path(sid: sid)
      expect(response).to have_http_status(:success)
    end
  end

  describe "POST /identity/password_reset" do
    context "with valid email" do
      it "sends password reset email and redirects to sign in" do
        expect {
          post identity_password_reset_path, params: {email: user.email}
        }.to have_enqueued_mail(UserMailer, :password_reset)

        expect(response).to redirect_to(sign_in_path)
      end
    end

    context "with nonexistent email" do
      it "does not send email and redirects with error" do
        expect {
          post identity_password_reset_path, params: {email: "<EMAIL>"}
        }.not_to have_enqueued_mail

        expect(response).to redirect_to(new_identity_password_reset_path)
        expect(flash[:alert]).to eq("You can't reset your password until you verify your email")
      end
    end

    context "with unverified email" do
      let(:unverified_user) { create(:user, verified: false) }

      it "does not send email and redirects with error" do
        expect {
          post identity_password_reset_path, params: {email: unverified_user.email}
        }.not_to have_enqueued_mail

        expect(response).to redirect_to(new_identity_password_reset_path)
        expect(flash[:alert]).to eq("You can't reset your password until you verify your email")
      end
    end
  end

  describe "PATCH /identity/password_reset" do
    context "with valid token" do
      it "updates password and redirects to sign in" do
        sid = user.generate_token_for(:password_reset)

        patch identity_password_reset_path, params: {
          sid: sid,
          password: "Secret6*4*2*",
          password_confirmation: "Secret6*4*2*"
        }
        expect(response).to redirect_to(sign_in_path)
      end
    end

    context "with expired token" do
      it "redirects to new password reset with error" do
        sid = user.generate_token_for(:password_reset)

        travel 30.minutes do
          patch identity_password_reset_path, params: {
            sid: sid,
            password: "Secret6*4*2*",
            password_confirmation: "Secret6*4*2*"
          }

          expect(response).to redirect_to(new_identity_password_reset_path)
          expect(flash[:alert]).to eq("That password reset link is invalid")
        end
      end
    end
  end
end
