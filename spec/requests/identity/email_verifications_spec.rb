require "rails_helper"

RSpec.describe "Identity::EmailVerifications", type: :request do
  let(:user) { create(:user, verified: false) }

  before do
    sign_in_user(user)
  end

  describe "POST /identity/email_verification" do
    it "sends a verification email and redirects to root" do
      expect {
        post identity_email_verification_path
      }.to have_enqueued_mail(UserMailer, :email_verification)

      expect(response).to redirect_to(root_path)
    end
  end

  describe "GET /identity/email_verification" do
    context "with valid token" do
      it "verifies email and redirects to root" do
        sid = user.generate_token_for(:email_verification)

        get identity_email_verification_path(sid: sid, email: user.email)
        expect(response).to redirect_to(root_path)
      end
    end

    context "with expired token" do
      it "redirects to edit email with error message" do
        sid = user.generate_token_for(:email_verification)

        travel 3.days do
          get identity_email_verification_path(sid: sid, email: user.email)
          expect(response).to redirect_to(edit_identity_email_path)
          expect(flash[:alert]).to eq("That email verification link is invalid")
        end
      end
    end
  end

  private

  def sign_in_user(user)
    session_record = create(:session, user: user)
    session[:session_token] = session_record.id
  end
end
