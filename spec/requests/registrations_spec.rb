require "rails_helper"

RSpec.describe "Registrations", type: :request do
  describe "GET /sign_up" do
    it "returns http success" do
      get sign_up_path
      expect(response).to have_http_status(:success)
    end
  end

  describe "POST /sign_up" do
    let(:valid_params) do
      {
        email: "la<PERSON><PERSON><PERSON>@hey.com",
        password: "Secret1*3*5*",
        password_confirmation: "Secret1*3*5*"
      }
    end

    it "creates a new user and redirects to root" do
      expect {
        post sign_up_path, params: valid_params
      }.to change(User, :count).by(1)

      expect(response).to redirect_to(root_path)
    end
  end
end
