require "rails_helper"

RSpec.describe "Passwords", type: :request do
  let(:user) { create(:user, password: "Secret1*3*5*", password_confirmation: "Secret1*3*5*") }

  before do
    sign_in_user(user)
  end

  describe "GET /password/edit" do
    it "returns http success" do
      get edit_password_path
      expect(response).to have_http_status(:success)
    end
  end

  describe "PATCH /password" do
    context "with valid password challenge" do
      it "updates password and redirects to root" do
        patch password_path, params: {
          password_challenge: "Secret1*3*5*",
          password: "Secret6*4*2*",
          password_confirmation: "Secret6*4*2*"
        }
        expect(response).to redirect_to(root_path)
      end
    end

    context "with invalid password challenge" do
      it "returns unprocessable entity with error message" do
        patch password_path, params: {
          password_challenge: "WrongPassword",
          password: "Secret6*4*2*",
          password_confirmation: "Secret6*4*2*"
        }

        expect(response).to have_http_status(:unprocessable_entity)
        expect(response.body).to include("Password challenge is invalid")
      end
    end
  end

  private

  def sign_in_user(user)
    session_record = create(:session, user: user)
    session[:session_token] = session_record.id
  end
end
