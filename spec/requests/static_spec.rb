require "rails_helper"

RSpec.describe "Statics", type: :request do
  describe "GET /" do
    context "when user is not authenticated" do
      it "renders the landing page" do
        get root_path
        expect(response).to have_http_status(:success)
      end

      it "does not redirect" do
        get root_path
        expect(response).not_to be_redirect
      end

      it "contains the TradeCrews branding" do
        get root_path
        expect(response.body).to include("TradeCrews")
      end

      it "contains sign in link" do
        get root_path
        expect(response.body).to include("Sign In")
      end

      it "contains get started link" do
        get root_path
        expect(response.body).to include("Get Started")
      end

      it "contains navigation links" do
        get root_path
        expect(response.body).to include("Find Contractors")
        expect(response.body).to include("For Contractors")
        expect(response.body).to include("How It Works")
      end
    end

    context "when user is authenticated" do
      let(:user) { create(:user) }

      before do
        sign_in_user(user)
      end

      it "redirects to dashboard" do
        get root_path
        expect(response).to redirect_to(dashboard_path)
      end

      it "does not render the landing page" do
        get root_path
        expect(response).not_to have_http_status(:success)
      end
    end

    context "with invalid session token" do
      before do
        # Mock the signed cookies to return an invalid token
        allow_any_instance_of(ActionDispatch::Cookies::SignedKeyRotatingCookieJar).to receive(:[]).with(:session_token).and_return("invalid-token")
      end

      it "renders the landing page" do
        get root_path
        expect(response).to have_http_status(:success)
      end
    end

    it "does not require authentication" do
      # This test ensures the skip_before_action :authenticate is working
      get root_path
      expect(response).not_to redirect_to(sign_in_path)
    end
  end

  private

  def sign_in_user(user)
    sign_in_as(user)
  end
end
