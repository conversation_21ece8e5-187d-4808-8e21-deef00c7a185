require "rails_helper"

RSpec.describe "AI Chat System", type: :system do
  let(:user) { create(:user) }

  before do
    # Set up authentication
    sign_in_as(user)

    # Mock AI service to avoid real API calls
    allow(AiService).to receive(:ask_question).and_return("This is a helpful response from <PERSON>.")
  end

  describe "AI Chat FAB" do
    before do
      visit dashboard_path
    end

    it "displays the floating action button" do
      expect(page).to have_css('[data-controller="ai-chat"]')
      expect(page).to have_css('button[data-action="click->ai-chat#toggle"]')
    end

    it "opens chat interface when FAB is clicked", js: true do
      find('button[data-action="click->ai-chat#toggle"]').click

      expect(page).to have_css('[data-ai-chat-target="interface"]:not(.hidden)')
      expect(page).to have_text("Jack - Project Planning Expert")
    end

    it "allows sending messages", js: true do
      find('button[data-action="click->ai-chat#toggle"]').click

      within('[data-ai-chat-target="interface"]') do
        fill_in "Ask Jack anything...", with: "How do I fix a leaky faucet?"
        click_button "Send"
      end

      expect(page).to have_text("How do I fix a leaky faucet?")
      expect(page).to have_text("This is a helpful response from Jack.")
    end

    it "closes when clicking outside", js: true do
      find('button[data-action="click->ai-chat#toggle"]').click
      expect(page).to have_css('[data-ai-chat-target="interface"]:not(.hidden)')

      find("body").click
      expect(page).to have_css('[data-ai-chat-target="interface"].hidden')
    end

    it "has quick action buttons", js: true do
      find('button[data-action="click->ai-chat#toggle"]').click

      expect(page).to have_button("Kitchen Tips")
      expect(page).to have_button("Tool Advice")
      expect(page).to have_link("Plan Project")
    end
  end

  describe "Full AI Chat Page" do
    before do
      visit ai_chat_path
    end

    it "displays the chat interface" do
      expect(page).to have_text("Chat with Jack")
      expect(page).to have_text("Your personal home improvement expert")
    end

    it "has navigation back to dashboard" do
      expect(page).to have_link("Back to Dashboard")
      click_link "Back to Dashboard"
      expect(current_path).to eq(dashboard_path)
    end

    it "allows sending messages", js: true do
      fill_in "Ask your question...", with: "What tools do I need for painting?"
      click_button "Send"

      expect(page).to have_text("What tools do I need for painting?")
      expect(page).to have_text("This is a helpful response from Jack.")
    end

    it "supports property context", js: true do
      property = create(:property, user: user, name: "Main House")
      visit ai_chat_path

      select "Main House", from: "Select property..."
      fill_in "Ask your question...", with: "What should I know about this property?"
      click_button "Send"

      expect(AiService).to have_received(:ask_question).with(
        "What should I know about this property?",
        hash_including(property: property)
      )
    end
  end

  describe "Authentication" do
    it "redirects unauthenticated users to sign in" do
      page.driver.browser.clear_cookies
      visit ai_chat_path
      expect(current_path).to eq(sign_in_path)
    end
  end
end
