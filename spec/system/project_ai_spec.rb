require "rails_helper"

RSpec.describe "Project AI System", type: :system do
  let(:user) { create(:user) }
  let(:session_record) { create(:session, user: user) }
  let(:property) { create(:property, user: user, name: "Main House") }

  before do
    # Set up authentication
    page.driver.browser.set_cookie("session_token", session_record.id)

    # Mock AI service to avoid real API calls
    allow(AiService).to receive(:conversational_project_planning).and_return({
      success: true,
      response: "That sounds like a great project! Tell me more about your kitchen renovation goals.",
      conversation_state: {stage: "gathering_requirements"},
      action: nil,
      data: nil
    })
  end

  describe "Project AI Chat Interface" do
    before do
      visit project_ai_path
    end

    it "displays the conversational interface" do
      expect(page).to have_text("AI Project Planner")
      expect(page).to have_text("Chat with <PERSON> to plan your home improvement project")
      expect(page).to have_text("Jack - Project Planning Expert")
    end

    it "shows welcome message from <PERSON>" do
      expect(page).to have_text("Hey there! I'm <PERSON>")
      expect(page).to have_text("30+ years")
      expect(page).to have_text("What kind of project are you thinking about?")
    end

    it "has navigation back to dashboard" do
      expect(page).to have_link("Back to Dashboard")
      click_link "Back to Dashboard"
      expect(current_path).to eq(dashboard_path)
    end

    it "displays quick starter questions" do
      expect(page).to have_button("🍳 Kitchen renovation")
      expect(page).to have_button("🚿 Bathroom update")
      expect(page).to have_button("🏠 Basement finishing")
      expect(page).to have_button("🔧 General repairs")
    end

    context "with multiple properties" do
      let!(:second_property) { create(:property, user: user, name: "Rental Property") }

      it "shows property selection" do
        visit project_ai_path
        expect(page).to have_select("Which property is this project for?")
        expect(page).to have_option("Main House")
        expect(page).to have_option("Rental Property")
      end
    end

    context "with single property" do
      it "does not show property selection" do
        expect(page).not_to have_select("Which property is this project for?")
      end
    end

    context "with no properties" do
      let(:user_without_properties) { create(:user) }
      let(:session_without_properties) { create(:session, user: user_without_properties) }

      before do
        page.driver.browser.set_cookie("session_token", session_without_properties.id)
        visit project_ai_path
      end

      it "shows message to add property first" do
        expect(page).to have_text("You need to add a property before planning a project")
        expect(page).to have_link("add a property")
      end

      it "disables the input" do
        expect(page).to have_field("Tell Jack about your project idea...", disabled: true)
      end
    end
  end

  describe "Conversational Flow", js: true do
    before do
      visit project_ai_path
    end

    it "allows sending messages to Jack" do
      fill_in "Tell Jack about your project idea...", with: "I want to renovate my kitchen"
      click_button "Send"

      expect(page).to have_text("I want to renovate my kitchen")
      expect(page).to have_text("That sounds like a great project!")
    end

    it "uses quick starter buttons" do
      click_button "🍳 Kitchen renovation"

      expect(page).to have_text("I want to renovate my kitchen")
      expect(page).to have_text("That sounds like a great project!")
    end

    it "calls AI service with correct parameters" do
      fill_in "Tell Jack about your project idea...", with: "Bathroom renovation"
      click_button "Send"

      expect(AiService).to have_received(:conversational_project_planning).with(
        hash_including(
          message: "Bathroom renovation",
          property: property,
          stage: "initial"
        )
      )
    end

    context "when AI suggests project creation" do
      before do
        allow(AiService).to receive(:conversational_project_planning).and_return({
          success: true,
          response: "Perfect! I have enough information to create your project plan.",
          conversation_state: {stage: "creating_project"},
          action: "show_project_summary",
          data: {
            name: "Kitchen Renovation",
            duration: "2-3 weeks",
            complexity: "Medium"
          }
        })
      end

      it "shows project summary" do
        fill_in "Tell Jack about your project idea...", with: "Kitchen with new cabinets"
        click_button "Send"

        expect(page).to have_text("📋 Project Summary")
        expect(page).to have_text("Kitchen Renovation")
        expect(page).to have_button("✅ Looks Good - Create Project")
        expect(page).to have_button("✏️ I want to modify something")
      end
    end
  end

  describe "Header Actions", js: true do
    before do
      visit project_ai_path
    end

    it "has dashboard navigation in header" do
      within(".bg-gradient-to-r.from-orange-400") do
        expect(page).to have_css('a[title="Return to Dashboard"]')
      end
    end

    it "has new conversation button" do
      within(".bg-gradient-to-r.from-orange-400") do
        expect(page).to have_css('button[title="Start New Conversation"]')
      end
    end
  end

  describe "Authentication" do
    it "redirects unauthenticated users to sign in" do
      page.driver.browser.clear_cookies
      visit project_ai_path
      expect(current_path).to eq(sign_in_path)
    end
  end

  describe "Error Handling", js: true do
    before do
      allow(AiService).to receive(:conversational_project_planning).and_return({
        success: false,
        response: "I'm having trouble processing that right now.",
        error: "API Error"
      })
      visit project_ai_path
    end

    it "handles AI service errors gracefully" do
      fill_in "Tell Jack about your project idea...", with: "Test message"
      click_button "Send"

      expect(page).to have_text("I'm having trouble processing that right now.")
    end
  end
end
