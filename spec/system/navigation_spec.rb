require "rails_helper"

RSpec.describe "Navigation System", type: :system do
  let(:user) { create(:user) }
  let(:session_record) { create(:session, user: user) }

  before do
    page.driver.browser.set_cookie("session_token", session_record.id)
  end

  describe "Dashboard Navigation" do
    before do
      visit dashboard_path
    end

    it "displays main navigation elements" do
      expect(page).to have_text("Dashboard")
      expect(page).to have_link("AI Project Creator")
      expect(page).to have_link("Add Property")
    end

    it "shows user menu" do
      expect(page).to have_css('[data-controller="dropdown"]') # User menu dropdown
    end

    it "displays AI chat FAB" do
      expect(page).to have_css('[data-controller="ai-chat"]')
    end
  end

  describe "AI Chat Navigation" do
    before do
      visit ai_chat_path
    end

    it "has breadcrumb navigation" do
      expect(page).to have_link("Back to Dashboard")
    end

    it "shows page title and description" do
      expect(page).to have_text("AI Assistant")
      expect(page).to have_text("Ask me anything about your home projects")
    end

    it "includes user menu" do
      expect(page).to have_css('[data-controller="dropdown"]')
    end

    it "navigates back to dashboard" do
      click_link "Back to Dashboard"
      expect(current_path).to eq(dashboard_path)
    end
  end

  describe "Project AI Navigation" do
    before do
      visit project_ai_path
    end

    it "has breadcrumb navigation" do
      expect(page).to have_link("Back to Dashboard")
    end

    it "shows page title and description" do
      expect(page).to have_text("AI Project Planner")
      expect(page).to have_text("Chat with Jack to plan your home improvement project")
    end

    it "has header actions" do
      within(".bg-gradient-to-r.from-orange-400") do
        expect(page).to have_css('a[title="Return to Dashboard"]')
        expect(page).to have_css('button[title="Start New Conversation"]')
      end
    end

    it "navigates back to dashboard from breadcrumb" do
      click_link "Back to Dashboard"
      expect(current_path).to eq(dashboard_path)
    end

    it "navigates back to dashboard from header icon", js: true do
      within(".bg-gradient-to-r.from-orange-400") do
        find('a[title="Return to Dashboard"]').click
      end
      expect(current_path).to eq(dashboard_path)
    end
  end

  describe "User Menu Navigation" do
    before do
      visit dashboard_path
    end

    it "contains expected menu items", js: true do
      # Open user menu (this might need adjustment based on actual implementation)
      find('[data-controller="dropdown"] button').click

      expect(page).to have_link("AI Project Creator")
      expect(page).to have_link("Profile") # Assuming these links exist
      expect(page).to have_link("Settings") # Adjust based on actual menu items
    end

    it "navigates to AI Project Creator from menu", js: true do
      find('[data-controller="dropdown"] button').click
      click_link "AI Project Creator"
      expect(current_path).to eq(project_ai_path)
    end
  end

  describe "Cross-Navigation Between AI Features" do
    it "navigates from FAB to full chat", js: true do
      visit dashboard_path

      # Open FAB
      find('button[data-action="click->ai-chat#toggle"]').click

      # Click "Continue conversation" link (assuming it exists)
      within('[data-ai-chat-target="interface"]') do
        click_link "General Chat" # Adjust based on actual link text
      end

      expect(current_path).to eq(ai_chat_path)
    end

    it "navigates from FAB to project AI", js: true do
      visit dashboard_path

      # Open FAB
      find('button[data-action="click->ai-chat#toggle"]').click

      # Click project planning link
      within('[data-ai-chat-target="interface"]') do
        click_link "Create Project"
      end

      expect(current_path).to eq(project_ai_path)
    end
  end

  describe "Responsive Navigation" do
    before do
      # Set mobile viewport
      page.driver.browser.manage.window.resize_to(375, 667)
    end

    after do
      # Reset to desktop viewport
      page.driver.browser.manage.window.resize_to(1280, 720)
    end

    it "maintains navigation functionality on mobile" do
      visit dashboard_path

      expect(page).to have_css('[data-controller="ai-chat"]') # FAB should be visible
      expect(page).to have_link("AI Project Creator") # Main navigation should work
    end

    it "FAB remains accessible on mobile", js: true do
      visit dashboard_path

      find('button[data-action="click->ai-chat#toggle"]').click
      expect(page).to have_css('[data-ai-chat-target="interface"]:not(.hidden)')
    end
  end

  describe "Navigation State Persistence" do
    it "maintains proper navigation context across page loads" do
      visit ai_chat_path
      expect(page).to have_link("Back to Dashboard")

      # Reload page
      visit current_path
      expect(page).to have_link("Back to Dashboard")
    end

    it "shows correct active states" do
      visit project_ai_path

      # Check that we're on the correct page
      expect(page).to have_text("AI Project Planner")
      expect(current_path).to eq(project_ai_path)
    end
  end

  describe "Error Page Navigation" do
    it "handles 404 errors gracefully" do
      visit "/nonexistent-page"
      expect(page.status_code).to eq(404)
      # Assuming there's a way to navigate back from 404 page
    end
  end
end
