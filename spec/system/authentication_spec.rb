require "rails_helper"

RSpec.describe "Authentication System", type: :system do
  let(:user) { create(:user, email: "<EMAIL>", password: "password123") }

  describe "Landing Page Behavior" do
    context "when user is not authenticated" do
      it "shows the landing page" do
        visit root_path
        expect(current_path).to eq(root_path)
        expect(page).to have_text("Welcome") # Assuming landing page has welcome text
      end

      it "does not redirect to dashboard" do
        visit root_path
        expect(current_path).not_to eq(dashboard_path)
      end
    end

    context "when user is authenticated" do
      before do
        sign_in_as(user)
      end

      it "redirects to dashboard" do
        visit root_path
        expect(current_path).to eq(dashboard_path)
      end

      it "does not show the landing page" do
        visit root_path
        expect(current_path).not_to eq(root_path)
      end
    end

    context "with invalid session token" do
      before do
        page.driver.browser.set_cookie("session_token", "invalid-token")
      end

      it "shows the landing page" do
        visit root_path
        expect(current_path).to eq(root_path)
      end
    end
  end

  describe "Protected Routes" do
    let(:protected_paths) do
      [
        dashboard_path,
        ai_chat_path,
        project_ai_path,
        properties_path,
        new_property_path
      ]
    end

    context "when user is not authenticated" do
      it "redirects to sign in page" do
        protected_paths.each do |path|
          visit path
          expect(current_path).to eq(sign_in_path)
        end
      end
    end

    context "when user is authenticated" do
      before do
        sign_in_as(user)
      end

      it "allows access to protected routes" do
        protected_paths.each do |path|
          visit path
          expect(current_path).to eq(path)
        end
      end
    end
  end

  describe "Sign In Process" do
    before do
      visit sign_in_path
    end

    it "displays sign in form" do
      expect(page).to have_field("Email")
      expect(page).to have_field("Password")
      expect(page).to have_button("Sign in")
    end

    context "with valid credentials" do
      it "signs in user and redirects to dashboard" do
        fill_in "Email", with: user.email
        fill_in "Password", with: "password123"
        click_button "Sign in"

        expect(current_path).to eq(dashboard_path)
        expect(page).to have_text("Dashboard") # Assuming dashboard has this text
      end
    end

    context "with invalid credentials" do
      it "shows error message" do
        fill_in "Email", with: user.email
        fill_in "Password", with: "wrongpassword"
        click_button "Sign in"

        expect(current_path).to eq(sign_in_path)
        expect(page).to have_text("Invalid email or password") # Assuming this error message
      end
    end
  end

  describe "Sign Up Process" do
    before do
      visit sign_up_path
    end

    it "displays sign up form" do
      expect(page).to have_field("Name")
      expect(page).to have_field("Email")
      expect(page).to have_field("Password")
      expect(page).to have_field("Password confirmation")
      expect(page).to have_button("Sign up")
    end

    context "with valid information" do
      it "creates account and redirects to onboarding" do
        fill_in "Name", with: "New User"
        fill_in "Email", with: "<EMAIL>"
        fill_in "Password", with: "password123"
        fill_in "Password confirmation", with: "password123"
        click_button "Sign up"

        expect(current_path).to eq(onboarding_property_path) # Assuming onboarding flow
      end
    end

    context "with invalid information" do
      it "shows validation errors" do
        fill_in "Name", with: ""
        fill_in "Email", with: "invalid-email"
        fill_in "Password", with: "123"
        click_button "Sign up"

        expect(page).to have_text("error") # Assuming error messages are shown
      end
    end
  end

  describe "Sign Out Process" do
    let(:session_record) { create(:session, user: user) }

    before do
      page.driver.browser.set_cookie("session_token", session_record.id)
      visit dashboard_path
    end

    it "signs out user and redirects to landing page" do
      # Assuming there's a sign out link in the user menu
      click_link "Sign out" # This might need to be adjusted based on actual implementation

      expect(current_path).to eq(root_path)

      # Verify user is actually signed out by trying to access protected route
      visit dashboard_path
      expect(current_path).to eq(sign_in_path)
    end
  end

  describe "Session Management" do
    let(:session_record) { create(:session, user: user) }

    it "maintains session across page visits" do
      page.driver.browser.set_cookie("session_token", session_record.id)

      visit dashboard_path
      expect(current_path).to eq(dashboard_path)

      visit ai_chat_path
      expect(current_path).to eq(ai_chat_path)

      visit project_ai_path
      expect(current_path).to eq(project_ai_path)
    end

    it "handles expired sessions" do
      # Create an old session (this would depend on your session expiration logic)
      old_session = create(:session, user: user, created_at: 1.month.ago)
      page.driver.browser.set_cookie("session_token", old_session.id)

      visit dashboard_path
      expect(current_path).to eq(sign_in_path)
    end
  end
end
