source "https://rubygems.org"

# --- Rails Core ---
gem "rails", "~> 8.0.2" # Main Rails framework
gem "propshaft" # Modern asset pipeline
gem "importmap-rails" # ESM import maps for JS
gem "jbuilder" # JSON API builder

# --- Database ---
gem "pg", "~> 1.1" # PostgreSQL adapter

# --- Web Server ---
gem "puma", ">= 5.0" # Web server

# --- Hotwire & SPA ---
gem "turbo-rails" # Turbo for SPA-like experience
gem "stimulus-rails" # Stimulus JS framework

# --- Styling ---
gem "tailwindcss-rails" # Tailwind CSS integration

# --- Authentication & Authorization ---
gem "authentication-zero" # Simple authentication
gem "omniauth" # OmniAuth base
gem "omniauth-facebook"
gem "omniauth-google-oauth2"
gem "omniauth-twitter"
gem "omniauth-entra-id"
gem "omniauth-rails_csrf_protection"

# --- Security ---
gem "bcrypt", "~> 3.1.7" # Password hashing
gem "pwned" # Password breach check
gem "rotp" # One-time password

# --- Geolocation & Notifications ---
gem "geocoder" # Geocoding
gem "noticed" # Notification framework

# --- Email ---
gem "resend" # Transactional email
gem "letter_opener" # Preview emails in browser

# --- Validations ---
gem "validates_timeliness" # Date/time validation
gem "active_storage_validations" # ActiveStorage validations

# --- View Components ---
gem "view_component", "~> 4.0.0.rc3" # ViewComponent for UI

# --- Utilities ---
gem "rqrcode" # QR code generation
gem "solid_cache" # Rails.cache adapter
gem "solid_queue" # Active Job adapter
gem "solid_cable" # Action Cable adapter
gem "bootsnap", require: false # Boot speedup
gem "tzinfo-data", platforms: %i[windows jruby] # Timezone data for Windows

# --- Deployment & Ops ---
gem "kamal", require: false # Docker deployment
gem "thruster", require: false # Asset caching/compression

# --- AI & ML ---
gem "ruby-openai" # AI agent functionality

# --- Optional/Image Processing ---
# gem "image_processing", "~> 1.2" # Active Storage variants

# --- Development & Test ---
group :development, :test do
  # Debugging & Analysis
  gem "debug", platforms: %i[mri windows], require: "debug/prelude"
  gem "brakeman", require: false # Security scanner
  gem "rubocop-rails-omakase", require: false # Ruby style

  # Testing
  gem "rspec-rails"
  gem "selenium-webdriver"
  gem "capybara"
  gem "factory_bot_rails"
  gem "faker"
  gem "shoulda-matchers"
  gem "dotenv-rails"
  gem "database_cleaner-active_record"
end

group :development do
  # Developer Tools
  gem "web-console" # Console on exception pages
  gem "annotaterb" # Annotate models
  gem "better_errors" # Better error pages
  gem "binding_of_caller" # Call stack inspection
end

group :test do
  gem "simplecov", require: false # Code coverage
end
