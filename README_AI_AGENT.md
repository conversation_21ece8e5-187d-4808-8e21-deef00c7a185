# Meet Jack - Your AI Home Project Assistant

This Rails application now includes <PERSON>, a wise and experienced AI assistant who helps homeowners with their projects. <PERSON> has a warm, fatherly personality and speaks from decades of hands-on experience working on homes.

## What Jack Can Do

Jack can help you with:

1. **Project Planning** - Create detailed, realistic project plans based on your property and requirements
2. **Expert Q&A** - Get practical advice on tools, materials, techniques, and project management
3. **Cost-Effective Solutions** - Learn from <PERSON>'s mistakes to avoid common pitfalls
4. **Safety Guidance** - Always emphasizes proper safety and preparation
5. **Contractor Management** - Advice on when to DIY vs when to call professionals

## Jack's Personality

Jack is designed to be:
- **Warm and Fatherly** - Like getting advice from an experienced neighbor
- **Practical and Realistic** - Honest about timelines, costs, and challenges
- **Experience-Based** - Shares personal anecdotes and lessons learned
- **Safety-Focused** - Always emphasizes doing things the right way
- **Accessible** - Uses plain language, not technical jargon

## Setup

### 1. Install Dependencies

```bash
bundle install
rails db:migrate
```

### 2. Environment Variables

Set up your API keys in your environment or Rails credentials:

```bash
# For OpenAI
export OPENAI_API_KEY="sk-your-openai-api-key"

# For Anthropic (Claude)
export ANTHROPIC_API_KEY="sk-ant-your-anthropic-api-key"
```

Alternatively, you can store API keys directly in the AI Agent configuration (though environment variables are recommended for security).

### 3. Supported Models

**OpenAI:**
- GPT-4o (Recommended for best quality)
- GPT-4o Mini (Faster, more cost-effective)

**Anthropic:**
- Claude 3.5 Sonnet (Recommended for best quality)
- Claude 3.5 Haiku (Faster, more cost-effective)

## Usage

### Creating an AI Agent

1. Navigate to the user menu and click "AI Agents"
2. Click "New AI Agent"
3. Configure:
   - **Name**: Give your agent a descriptive name
   - **Provider**: Choose between OpenAI or Anthropic
   - **Model**: Select the specific model variant
   - **API Key**: Optional if environment variables are set
   - **System Prompt**: Optional custom instructions

### Using the AI Agent

#### Project Generation
1. Select a property from your list
2. Describe your project requirements
3. Click "Generate Project Plan"
4. Review the AI-generated plan
5. Click "Create This Project" to add it to your projects

#### Q&A Assistant
1. Optionally select context (property/project)
2. Ask your question
3. Get expert advice and recommendations

### Features

- **Context-aware responses**: AI considers your specific properties and projects
- **Structured project creation**: AI generates detailed project plans with tasks and timelines
- **Conversation history**: Track your interactions with the AI
- **Multiple model support**: Switch between different AI providers and models
- **Secure API key management**: Store keys in environment variables or encrypted in database

## Integration with Context7

The AI agents use Context7 for accessing up-to-date documentation and best practices. This ensures the AI provides current, accurate information about:

- Construction techniques
- Building codes and regulations
- Material recommendations
- Safety guidelines
- Industry best practices

## Architecture

### Models
- `AiAgent`: Manages AI configuration and API interactions
- `User`: Has many AI agents
- `Project`: Can be created from AI suggestions

### Controllers
- `AiAgentsController`: CRUD operations and AI interactions
- `ProjectsController`: Enhanced to support AI-generated projects

### Key Methods
- `AiAgent#generate_project_suggestions`: Creates structured project plans
- `AiAgent#answer_question`: Provides expert Q&A responses
- `ProjectsController#create_from_ai`: Creates projects from AI suggestions

## Security Considerations

- API keys are encrypted when stored in database
- Environment variables are preferred for API key storage
- User authentication required for all AI agent operations
- Users can only access their own AI agents and data

## Error Handling

The system includes comprehensive error handling:
- API timeouts and connection errors
- Invalid API keys
- Rate limiting
- Malformed responses

All errors are logged and user-friendly messages are displayed.