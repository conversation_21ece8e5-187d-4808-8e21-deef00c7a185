class AiService
  API_KEY = Rails.application.credentials.dig(:openai, :api_key) || ENV["OPENAI_API_KEY"]

  def self.ask_question(question, context = {})
    new.ask_question(question, context)
  end

  def self.generate_project(property, requirements)
    new.generate_project(property, requirements)
  end

  def self.conversational_project_planning(message:, property:, conversation_state:, stage:)
    new.conversational_project_planning(
      message: message,
      property: property,
      conversation_state: conversation_state,
      stage: stage
    )
  end

  def ask_question(question, context = {})
    system_prompt = build_general_system_prompt(context)

    response = make_api_call([
      {role: "system", content: system_prompt},
      {role: "user", content: question}
    ])

    response.dig("choices", 0, "message", "content") || "Sorry, I couldn't process your question right now."
  end

  def generate_project(property, requirements)
    system_prompt = build_project_system_prompt(property)
    user_prompt = "Create a detailed project plan for: #{requirements}"

    response = make_api_call([
      {role: "system", content: system_prompt},
      {role: "user", content: user_prompt}
    ])

    response.dig("choices", 0, "message", "content") || "Sorry, I couldn't generate a project plan right now."
  end

  def conversational_project_planning(message:, property:, conversation_state:, stage:)
    system_prompt = build_conversational_project_prompt(property, conversation_state, stage)

    # Build conversation history
    messages = [
      {role: "system", content: system_prompt}
    ]

    # Add conversation history if available
    conversation_state["messages"]&.each do |msg|
      messages << {role: msg["role"], content: msg["content"]}
    end

    # Add current user message
    messages << {role: "user", content: message}

    response = make_api_call(messages)
    ai_response = response.dig("choices", 0, "message", "content")

    # Parse the AI response for structured data
    parsed_response = parse_conversational_response(ai_response, conversation_state, stage)

    # Update conversation history
    updated_state = conversation_state.dup
    updated_state["messages"] ||= []
    updated_state["messages"] << {role: "user", content: message}
    updated_state["messages"] << {role: "assistant", content: ai_response}

    {
      success: true,
      response: parsed_response[:response],
      conversation_state: updated_state.merge(parsed_response[:state_updates] || {}),
      action: parsed_response[:action],
      data: parsed_response[:data]
    }
  rescue => e
    Rails.logger.error "Conversational AI Error: #{e.message}"
    {
      success: false,
      response: "I'm having trouble understanding that. Could you try explaining your project idea differently?",
      error: e.message
    }
  end

  private

  def openai_client
    @openai_client ||= OpenAI::Client.new(access_token: API_KEY)
  end

  def make_api_call(messages)
    return mock_response if Rails.env.development? && API_KEY.blank?

    raise "OpenAI API key not configured" if API_KEY.blank?

    begin
      response = openai_client.chat(
        parameters: {
          model: "gpt-4o-mini",
          messages: messages,
          max_tokens: 1000,
          temperature: 0.7
        }
      )

      if response["error"]
        Rails.logger.error "OpenAI API Error: #{response["error"]}"
        raise "API request failed: #{response["error"]["message"]}"
      end

      response
    rescue => e
      Rails.logger.error "OpenAI API Error: #{e.message}"
      Rails.logger.error e.backtrace.join("\n")
      raise "API request failed: #{e.message}"
    end
  end

  def build_general_system_prompt(context)
    prompt = <<~PROMPT
      You are Jack, a friendly and experienced home improvement contractor with over 30 years of hands-on experience.
      You've worked on everything from small repairs to major renovations, and you love sharing practical advice.

      Your personality:
      - Warm, approachable, and encouraging
      - Speaks from real experience, not just theory
      - Gives practical, actionable advice
      - Sometimes shares brief stories from your experience
      - Always prioritizes safety
      - Honest about when something needs a professional

      Keep responses conversational and helpful. If you don't know something specific, say so honestly.
    PROMPT

    if context[:property]
      prompt += "\n\nContext: The user is asking about their #{context[:property].property_type} property: #{context[:property].name}"
    end

    if context[:project]
      prompt += "\n\nContext: This relates to their project: #{context[:project].name} - #{context[:project].description}"
    end

    prompt
  end

  def build_project_system_prompt(property)
    <<~PROMPT
      You are Jack, an experienced contractor creating detailed project plans. You have 30+ years of experience
      in home improvement and construction.

      Create a comprehensive project plan that includes:
      1. Project overview and description
      2. Estimated timeline (in days)
      3. List of tasks with required trades
      4. Complexity assessment (Low/Medium/High)
      5. Key considerations and potential challenges

      Property context: #{property.property_type} - #{property.name}

      Format your response as a detailed plan that a homeowner can understand and use to move forward.
      Be specific about trades needed (electrician, plumber, carpenter, etc.) and realistic about timelines.

      If the project is complex or potentially dangerous, recommend getting professional consultations.
    PROMPT
  end

  def build_conversational_project_prompt(property, conversation_state, stage)
    base_prompt = <<~PROMPT
      You are Jack, an experienced contractor with 30+ years in home improvement. You're having a conversation
      with a homeowner to help them plan a project step by step.

      Your conversation style:
      - Friendly, encouraging, and professional
      - Ask clarifying questions to understand their needs
      - Share relevant experience and insights
      - Guide them through the planning process naturally
      - Be specific about trades, materials, and realistic timelines

      Current conversation stage: #{stage}
      Property: #{property&.property_type} - #{property&.name}

      Conversation guidelines by stage:
      - initial: Get basic project idea, ask clarifying questions
      - gathering_requirements: Dive deeper into specifics (size, materials, budget, timeline)
      - confirming_details: Summarize and confirm project scope
      - creating_project: Finalize project plan and create structured data

      When you have enough information to create a project plan, respond with:
      "READY_TO_CREATE_PROJECT" followed by your normal response.
    PROMPT

    if conversation_state["project_data"]
      base_prompt += "\n\nCurrent project data collected: #{conversation_state["project_data"].to_json}"
    end

    base_prompt
  end

  def parse_conversational_response(ai_response, conversation_state, stage)
    result = {
      response: ai_response,
      state_updates: {},
      action: nil,
      data: nil
    }

    # Check for special commands in the response
    if ai_response.include?("READY_TO_CREATE_PROJECT")
      result[:action] = "show_project_summary"
      result[:state_updates]["stage"] = "creating_project"
      result[:response] = ai_response.gsub("READY_TO_CREATE_PROJECT", "").strip

      # Extract project data from conversation
      result[:data] = extract_project_data_from_conversation(conversation_state)
    end

    # Update stage based on conversation progress
    if stage == "initial" && ai_response.downcase.include?("tell me more")
      result[:state_updates]["stage"] = "gathering_requirements"
    end

    result
  end

  def extract_project_data_from_conversation(conversation_state)
    # Basic structure for now - could be enhanced with AI extraction
    {
      name: conversation_state["project_type"] || "Home Improvement Project",
      description: "Project planned through conversation with Jack",
      duration: "TBD",
      complexity: "Medium"
    }
  end

  def mock_response
    {
      "choices" => [
        {
          "message" => {
            "content" => "Hi there! I'm Jack, and I'd love to help with your home project question. " \
                        "However, I need an OpenAI API key to be configured to give you real advice. " \
                        "For now, I'm just a friendly placeholder! Once the API key is set up, " \
                        "I'll be able to share my 30+ years of home improvement experience with you."
          }
        }
      ]
    }
  end
end
