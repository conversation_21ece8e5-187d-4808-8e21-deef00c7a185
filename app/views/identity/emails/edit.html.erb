<!-- Change/Verify Email Page -->
<div class="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
  <div class="sm:mx-auto sm:w-full sm:max-w-md">
    <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">
      <% if Current.user.verified? %>
        Change your email
      <% else %>
        Verify your email
      <% end %>
    </h2>
    <% unless Current.user.verified? %>
      <p class="mt-2 text-center text-sm text-gray-600">
        We sent a verification email to the address below. Check that email and follow those instructions to confirm it's your email address.
      </p>
    <% end %>
  </div>

  <div class="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
    <div class="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
      <%= render 'shared/flash_messages' %>
      
      <% unless Current.user.verified? %>
        <div class="mb-6 text-center">
          <%= button_to "Re-send verification email", identity_email_verification_path,
              class: "inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-tradecrews-orange-600 bg-tradecrews-orange-100 hover:bg-tradecrews-orange-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-tradecrews-orange-500" %>
        </div>
      <% end %>
      
      <!-- Model Error Messages -->
      <% if @user && @user.errors.any? %>
        <div class="mb-4 rounded-md bg-red-50 p-4 border border-red-200">
          <div class="flex">
            <div class="flex-shrink-0">
              <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
              </svg>
            </div>
            <div class="ml-3">
              <h3 class="text-sm font-medium text-red-800">
                <%= pluralize(@user.errors.count, "error") %> prohibited this email from being changed:
              </h3>
              <div class="mt-2 text-sm text-red-700">
                <ul class="list-disc pl-5 space-y-1">
                  <% @user.errors.each do |error| %>
                    <li><%= error.full_message %></li>
                  <% end %>
                </ul>
              </div>
            </div>
          </div>
        </div>
      <% end %>

<%= form_with(url: identity_email_path, method: :patch, html: { class: "space-y-6" }) do |form| %>

        <div>
          <%= form.label :email, "New email", class: "block text-sm font-medium text-gray-700" %>
          <div class="mt-1">
            <%= form.email_field :email, required: true, autofocus: true,
                class: "appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-tradecrews-orange-500 focus:border-tradecrews-orange-500 sm:text-sm" %>
          </div>
        </div>

        <div>
          <%= form.label :password_challenge, "Current password", class: "block text-sm font-medium text-gray-700" %>
          <div class="mt-1">
            <%= form.password_field :password_challenge, required: true, autocomplete: "current-password",
                class: "appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-tradecrews-orange-500 focus:border-tradecrews-orange-500 sm:text-sm" %>
          </div>
        </div>

        <div>
          <%= form.submit "Save changes",
              class: "w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-tradecrews-orange-600 hover:bg-tradecrews-orange-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-tradecrews-orange-500 transition-colors duration-200" %>
        </div>
      <% end %>

      <div class="mt-6 text-center">
        <%= link_to "Back", root_path,
          class: "text-sm text-tradecrews-blue-600 hover:text-tradecrews-blue-500 font-medium" %>
      </div>
    </div>
  </div>
</div>
