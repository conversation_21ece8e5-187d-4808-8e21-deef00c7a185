<!-- Password Reset Page -->
<div class="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
  <div class="sm:mx-auto sm:w-full sm:max-w-md">
    <!-- Logo -->
    <div class="flex justify-center">
      <div class="flex items-center">
        <%= image_tag "tc-logomark.webp", class: "h-12 w-12 mr-3" %>
        <h1 class="text-3xl font-bold text-tradecrews-blue-600">TradeCrews</h1>
      </div>
    </div>
    <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">
      Reset your password
    </h2>
    <p class="mt-2 text-center text-sm text-gray-600">
      Enter your email address and we'll send you a link to reset your password.
    </p>
  </div>

  <div class="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
    <div class="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
      <%= render 'shared/flash_messages' %>
      <%= form_with url: identity_password_reset_path, local: true, html: { class: "space-y-6" } do |f| %>
        <div>
          <%= f.label :email, class: "block text-sm font-medium text-gray-700" %>
          <div class="mt-1">
            <%= f.email_field :email, autofocus: true, autocomplete: "email",
                class: "appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-tradecrews-orange-500 focus:border-tradecrews-orange-500 sm:text-sm" %>
          </div>
        </div>
        <div>
          <%= f.submit "Send reset instructions",
              class: "w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-tradecrews-orange-600 hover:bg-tradecrews-orange-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-tradecrews-orange-500 transition-colors duration-200" %>
        </div>
      <% end %>
      <div class="mt-6 text-center">
        <%= link_to "Back to sign in", sign_in_path,
          class: "text-sm text-tradecrews-blue-600 hover:text-tradecrews-blue-500 font-medium" %>
      </div>
    </div>
  </div>
</div>
