<!-- User <PERSON>u Dropdown -->
<div class="relative" data-controller="dropdown">
  <!-- User <PERSON><PERSON> -->
  <button type="button" 
          class="flex items-center text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-tradecrews-orange-500" 
          data-action="click->dropdown#toggle"
          data-dropdown-target="button"
          aria-expanded="false" 
          aria-haspopup="true">
    <span class="sr-only">Open user menu</span>
    <div class="flex items-center space-x-3">
      <!-- User Avatar -->
      <div class="w-8 h-8 bg-tradecrews-orange-500 rounded-full flex items-center justify-center">
        <span class="text-sm font-medium text-white">
          <%= Current.user.name.first.upcase if Current.user.name %>
        </span>
      </div>
      <!-- User Name and Role -->
      <div class="hidden md:block text-left">
        <div class="text-sm font-medium text-gray-900"><%= Current.user.name %></div>
        <div class="text-xs text-gray-500 capitalize"><%= Current.user.role %></div>
      </div>
      <!-- Dropdown Arrow -->
      <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
      </svg>
    </div>
  </button>

  <!-- Dropdown Menu -->
  <div class="hidden absolute right-0 mt-2 w-56 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 focus:outline-none z-50"
       data-dropdown-target="menu"
       role="menu" 
       aria-orientation="vertical" 
       aria-labelledby="user-menu-button">
    <div class="py-1" role="none">
      <!-- Profile Section -->
      <div class="px-4 py-2 border-b border-gray-100">
        <p class="text-sm font-medium text-gray-900"><%= Current.user.name %></p>
        <p class="text-sm text-gray-500"><%= Current.user.email %></p>
      </div>

      <!-- Profile Management -->
      <div class="py-1">
        <%= link_to profile_path, 
            class: "flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-gray-900",
            role: "menuitem" do %>
          <svg class="mr-3 h-4 w-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
          </svg>
          Profile & Settings
        <% end %>
        
        <%= link_to project_ai_path, 
            class: "flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-gray-900",
            role: "menuitem" do %>
          <svg class="mr-3 h-4 w-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-4m-5 0H3m2 0h3M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
          </svg>
          AI Project Creator
        <% end %>
      </div>

      <!-- Sign Out -->
      <div class="py-1 border-t border-gray-100">
        <%= button_to sign_out_path, method: :delete,
            class: "flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-gray-900",
            role: "menuitem",
            form: { class: "w-full" } do %>
          <svg class="mr-3 h-4 w-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path>
          </svg>
          Sign Out
        <% end %>
      </div>
    </div>
  </div>
</div>