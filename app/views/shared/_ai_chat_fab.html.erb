<!-- AI Chat Floating Action Button -->
<div class="fixed bottom-6 right-6 z-50" 
     data-controller="ai-chat"
     data-ai-chat-ask-url-value="<%= ai_chat_ask_path %>"
     data-ai-chat-jack-image-url-value="<%= asset_path('jack.png') %>">
  
  <!-- FAB Button -->
  <button class="w-16 h-16 bg-gradient-to-br from-orange-400 to-red-500 hover:from-orange-500 hover:to-red-600 text-white rounded-full shadow-lg hover:shadow-xl transition-all duration-200 flex items-center justify-center group"
          data-action="click->ai-chat#toggle">
    <%= image_tag "jack.png", class: "w-12 h-12 rounded-full object-cover border-2 border-white shadow-sm", alt: "Jack - AI Assistant" %>
  </button>

  <!-- Chat Interface -->
  <div class="hidden absolute bottom-16 right-0 w-96 h-[500px] bg-white rounded-lg shadow-2xl border border-gray-200"
       data-ai-chat-target="interface"
       data-action="click->ai-chat#preventClose">
    
    <!-- Chat Header -->
    <div class="bg-gradient-to-r from-orange-400 to-red-500 text-white p-4 rounded-t-lg flex items-center justify-between">
      <div class="flex items-center space-x-3">
        <%= image_tag "jack.png", class: "w-10 h-10 rounded-full object-cover border-2 border-white", alt: "Jack" %>
        <div>
          <h3 class="font-semibold">Jack</h3>
          <p class="text-sm text-orange-100">Your Home Project Expert</p>
        </div>
      </div>
      <div class="flex items-center space-x-2">
        <button class="text-orange-100 hover:text-white transition-colors"
                data-action="click->ai-chat#minimize"
                id="minimize-chat">
          <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 12H4"></path>
          </svg>
        </button>
        <button class="text-orange-100 hover:text-white transition-colors"
                data-action="click->ai-chat#close">
          <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
          </svg>
        </button>
      </div>
    </div>

    <!-- Chat Messages -->
    <div class="flex-1 overflow-y-auto p-4 space-y-4 bg-gray-50" data-ai-chat-target="messages">
      <!-- Welcome Message -->
      <div class="flex items-start space-x-3">
        <%= image_tag "jack.png", class: "w-8 h-8 rounded-full object-cover flex-shrink-0", alt: "Jack" %>
        <div class="bg-white rounded-lg p-3 shadow-sm max-w-xs">
          <p class="text-gray-800 text-sm">Hey there! I'm Jack, your home project expert. I've got 30+ years of experience and I'm here to help with any questions you have. What can I help you with today?</p>
        </div>
      </div>
    </div>

    <!-- Quick Actions -->
    <div class="px-4 py-2 border-t border-gray-200 bg-white">
      <div class="flex space-x-2 mb-3">
        <button class="flex-1 bg-orange-100 hover:bg-orange-200 text-orange-700 py-2 px-3 rounded-md text-xs font-medium transition-colors" 
                data-question="What's the best way to start a kitchen renovation?"
                data-action="click->ai-chat#quickAction">
          Kitchen Tips
        </button>
        <button class="flex-1 bg-blue-100 hover:bg-blue-200 text-blue-700 py-2 px-3 rounded-md text-xs font-medium transition-colors" 
                data-question="What tools do I need for basic home repairs?"
                data-action="click->ai-chat#quickAction">
          Tool Advice
        </button>
        <%= link_to project_ai_path, class: "flex-1 bg-green-100 hover:bg-green-200 text-green-700 py-2 px-3 rounded-md text-xs font-medium text-center transition-colors" do %>
          Plan Project
        <% end %>
      </div>
    </div>

    <!-- Chat Input -->
    <div class="p-4 border-t border-gray-200 bg-white rounded-b-lg">
      <form class="flex space-x-2" 
            data-ai-chat-target="form"
            data-action="submit->ai-chat#submitMessage">
        <input type="text" 
               class="flex-1 border-gray-300 rounded-full px-4 py-2 focus:ring-orange-500 focus:border-orange-500 text-sm" 
               placeholder="Ask Jack anything..."
               data-ai-chat-target="input">
        <button type="submit" 
                class="bg-gradient-to-r from-orange-400 to-red-500 hover:from-orange-500 hover:to-red-600 text-white p-2 rounded-full transition-all duration-200">
          <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"></path>
          </svg>
        </button>
      </form>
    </div>
  </div>
</div>

