<%= form_with model: property, local: true, class: "space-y-6" do |f| %>

  <!-- Model Error Messages -->
  <% if property.errors.any? %>
    <div class="rounded-md bg-red-50 p-4 border border-red-200">
      <div class="flex">
        <div class="flex-shrink-0">
          <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
          </svg>
        </div>
        <div class="ml-3">
          <h3 class="text-sm font-medium text-red-800">
            <%= pluralize(property.errors.count, "error") %> prohibited this property from being saved:
          </h3>
          <div class="mt-2 text-sm text-red-700">
            <ul class="list-disc pl-5 space-y-1">
              <% property.errors.full_messages.each do |message| %>
                <li><%= message %></li>
              <% end %>
            </ul>
          </div>
        </div>
      </div>
    </div>
  <% end %>

  <!-- Property Name -->
  <div>
    <%= f.label :name, "Property Name", class: "block text-sm font-medium text-gray-700 mb-2" %>
    <%= f.text_field :name,
        class: "block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-tradecrews-orange-500 focus:border-tradecrews-orange-500",
        placeholder: "e.g., Main House, Rental Property, Office Building" %>
    <p class="mt-1 text-sm text-gray-500">Give your property a name to easily identify it</p>
  </div>

  <!-- Property Type -->
  <div>
    <%= f.label :property_type, "Property Type", class: "block text-sm font-medium text-gray-700 mb-2" %>
    <%= f.select :property_type,
        options_for_select([
          ['Single Family Home', 'single_family'],
          ['Condominium', 'condo'],
          ['Townhouse', 'townhouse'],
          ['Multi Family', 'multi_family'],
          ['Mobile Home', 'mobile_home'],
          ['Other', 'other']
        ], property.property_type),
        { prompt: 'Select property type' },
        { class: "block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-tradecrews-orange-500 focus:border-tradecrews-orange-500" } %>
  </div>

  <!-- Address Section -->
  <div class="border-t border-gray-200 pt-6">
    <h3 class="text-lg font-medium text-gray-900 mb-4">Property Address</h3>

    <%= f.fields_for :address do |address_form| %>
      <!-- Street Address -->
      <div class="mb-4">
        <%= address_form.label :street, "Street Address", class: "block text-sm font-medium text-gray-700 mb-2" %>
        <%= address_form.text_field :street,
            class: "block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-tradecrews-orange-500 focus:border-tradecrews-orange-500",
            placeholder: "123 Main Street" %>
      </div>

      <!-- City, State, Zip -->
      <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div>
          <%= address_form.label :city, "City", class: "block text-sm font-medium text-gray-700 mb-2" %>
          <%= address_form.text_field :city,
              class: "block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-tradecrews-orange-500 focus:border-tradecrews-orange-500" %>
        </div>
        <div>
          <%= address_form.label :state, "State", class: "block text-sm font-medium text-gray-700 mb-2" %>
          <%= address_form.text_field :state,
              class: "block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-tradecrews-orange-500 focus:border-tradecrews-orange-500" %>
        </div>
        <div>
          <%= address_form.label :zip_code, "ZIP Code", class: "block text-sm font-medium text-gray-700 mb-2" %>
          <%= address_form.text_field :zip_code,
              class: "block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-tradecrews-orange-500 focus:border-tradecrews-orange-500" %>
        </div>
      </div>
    <% end %>
  </div>

  <!-- Current Property Image (only show for existing properties) -->
  <% if property.persisted? && property.image.attached? %>
    <div class="border-t border-gray-200 pt-6">
      <h3 class="text-lg font-medium text-gray-900 mb-4">Current Property Photo</h3>
      <div class="flex items-start space-x-4">
        <%= image_tag property.image, class: "h-32 w-32 object-cover rounded-lg border border-gray-300" %>
        <div>
          <p class="text-sm text-gray-600 mb-2">Current photo for this property</p>
          <p class="text-xs text-gray-500">Upload a new photo below to replace this one</p>
        </div>
      </div>
    </div>
  <% end %>

  <!-- Property Image Upload -->
  <div class="border-t border-gray-200 pt-6">
    <h3 class="text-lg font-medium text-gray-900 mb-4">
      <% if property.persisted? && property.image.attached? %>
        Update Property Photo (Optional)
      <% else %>
        Property Photo (Optional)
      <% end %>
    </h3>
    <p class="text-sm text-gray-600 mb-4">Add a photo to help contractors better understand your property</p>

    <div class="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-md hover:border-tradecrews-orange-400 transition-colors duration-200" id="drop-zone" data-controller="property-image" data-property-image-target="dropZone">
      <div class="space-y-1 text-center">
        <svg class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48" aria-hidden="true">
          <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
        </svg>
        <div class="flex text-sm text-gray-600">
          <label for="property_image" class="relative cursor-pointer bg-white rounded-md font-medium text-tradecrews-orange-600 hover:text-tradecrews-orange-500 focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-tradecrews-orange-500">
            <span>Upload a file</span>
            <%= f.file_field :image, id: "property_image", class: "sr-only", accept: "image/*", data: { 'property-image-target': 'fileInput' } %>
          </label>
          <p class="pl-1">or drag and drop</p>
        </div>
        <p class="text-xs text-gray-500">PNG, JPG, GIF up to 10MB</p>
      </div>
    </div>

    <!-- Image Preview -->
    <div id="image-preview" class="mt-4 hidden" data-property-image-target="imagePreview">
      <div class="relative">
        <img id="preview-image" class="h-32 w-32 object-cover rounded-lg border border-gray-300" src="" alt="Property preview" data-property-image-target="previewImage">
        <button type="button" id="remove-image" class="absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600 transition-colors duration-200" data-property-image-target="removeButton">
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
          </svg>
        </button>
      </div>
    </div>
  </div>

  <!-- Description -->
  <div>
    <%= f.label :description, "Property Description (Optional)", class: "block text-sm font-medium text-gray-700 mb-2" %>
    <%= f.text_area :description, rows: 4,
        class: "block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-tradecrews-orange-500 focus:border-tradecrews-orange-500",
        placeholder: "Describe your property, any special features, or specific areas where you might need work done..." %>
  </div>

  <!-- Submit Buttons -->
  <div class="flex justify-between pt-6">
    <%= link_to cancel_path, 
        class: "bg-gray-300 hover:bg-gray-400 text-gray-700 px-6 py-3 rounded-lg font-medium transition-colors" do %>
      Cancel
    <% end %>
    <%= f.submit submit_text,
        class: "bg-tradecrews-orange-600 hover:bg-tradecrews-orange-700 text-white px-8 py-3 rounded-lg font-semibold transition-colors duration-200" %>
  </div>

<% end %>