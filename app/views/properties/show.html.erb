<!-- Property Details -->
<div class="min-h-screen bg-gray-50">
  <!-- Header -->
  <div class="bg-white shadow">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="flex justify-between items-center py-6">
        <div>
          <h1 class="text-3xl font-bold text-gray-900"><%= @property.name %></h1>
          <p class="text-gray-600">Property details and project history</p>
        </div>
        <div class="flex items-center space-x-4">
          <%= link_to "Back to Properties", properties_path,
              class: "text-tradecrews-blue-600 hover:text-tradecrews-blue-500 font-medium" %>
          <%= link_to "Edit Property", edit_property_path(@property),
              class: "bg-tradecrews-orange-600 hover:bg-tradecrews-orange-700 text-white px-4 py-2 rounded-lg font-medium transition-colors" %>
        </div>
      </div>
    </div>
  </div>

  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <%= render 'shared/flash_messages' %>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
      <!-- Property Information -->
      <div class="lg:col-span-2 space-y-8">
        <!-- Property Details Card -->
        <div class="bg-white rounded-lg shadow">
          <div class="px-6 py-4 border-b border-gray-200">
            <h2 class="text-lg font-semibold text-gray-900">Property Information</h2>
          </div>
          <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Property Name</label>
                <p class="text-sm text-gray-900"><%= @property.name %></p>
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Property Type</label>
                <p class="text-sm text-gray-900"><%= @property.property_type.humanize %></p>
              </div>
              <% if @property.address %>
                <div class="md:col-span-2">
                  <label class="block text-sm font-medium text-gray-700 mb-1">Address</label>
                  <p class="text-sm text-gray-900">
                    <%= @property.address.street %><br>
                    <%= @property.address.city %>, <%= @property.address.state %> <%= @property.address.zip_code %>
                  </p>
                </div>
              <% end %>
              <% if @property.description.present? %>
                <div class="md:col-span-2">
                  <label class="block text-sm font-medium text-gray-700 mb-1">Description</label>
                  <p class="text-sm text-gray-900"><%= @property.description %></p>
                </div>
              <% end %>
            </div>
          </div>
        </div>

        <!-- Projects Section -->
        <div class="bg-white rounded-lg shadow">
          <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
            <h2 class="text-lg font-semibold text-gray-900">Projects</h2>
            <button class="bg-tradecrews-orange-600 hover:bg-tradecrews-orange-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
              New Project
            </button>
          </div>
          <div class="p-6">
            <% if @projects.any? %>
              <div class="space-y-4">
                <% @projects.each do |project| %>
                  <div class="border border-gray-200 rounded-lg p-4">
                    <div class="flex justify-between items-start mb-2">
                      <h3 class="text-lg font-medium text-gray-900"><%= project.name %></h3>
                      <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">
                        <%= project.status.humanize %>
                      </span>
                    </div>
                    <% if project.description.present? %>
                      <p class="text-sm text-gray-600 mb-3"><%= project.description %></p>
                    <% end %>
                    <div class="flex justify-between items-center text-sm text-gray-500">
                      <span>Created <%= time_ago_in_words(project.created_at) %> ago</span>
                      <span><%= pluralize(project.tasks.count, 'task') %></span>
                    </div>
                  </div>
                <% end %>
              </div>
            <% else %>
              <div class="text-center py-8">
                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                </svg>
                <h3 class="mt-2 text-sm font-medium text-gray-900">No projects yet</h3>
                <p class="mt-1 text-sm text-gray-500">Get started by creating your first project for this property.</p>
                <div class="mt-6">
                  <button class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-tradecrews-orange-600 hover:bg-tradecrews-orange-700">
                    Create Project
                  </button>
                </div>
              </div>
            <% end %>
          </div>
        </div>
      </div>

      <!-- Sidebar -->
      <div class="space-y-6">
        <!-- Property Image -->
        <div class="bg-white rounded-lg shadow">
          <div class="px-6 py-4 border-b border-gray-200">
            <h2 class="text-lg font-semibold text-gray-900">Property Photo</h2>
          </div>
          <div class="p-6">
            <% if @property.image.attached? %>
              <%= image_tag @property.image, class: "w-full h-48 object-cover rounded-lg border border-gray-300" %>
            <% else %>
              <div class="w-full h-48 bg-gray-100 rounded-lg flex items-center justify-center border border-gray-300">
                <div class="text-center">
                  <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                  </svg>
                  <p class="mt-2 text-sm text-gray-500">No photo uploaded</p>
                </div>
              </div>
            <% end %>
          </div>
        </div>

        <!-- Quick Actions -->
        <div class="bg-white rounded-lg shadow">
          <div class="px-6 py-4 border-b border-gray-200">
            <h2 class="text-lg font-semibold text-gray-900">Quick Actions</h2>
          </div>
          <div class="p-6 space-y-3">
            <%= link_to edit_property_path(@property),
                class: "flex items-center p-3 rounded-lg hover:bg-gray-50 transition-colors" do %>
              <svg class="w-5 h-5 text-gray-400 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
              </svg>
              <span class="text-sm font-medium text-gray-900">Edit Property</span>
            <% end %>

            <button class="flex items-center p-3 rounded-lg hover:bg-gray-50 transition-colors w-full text-left">
              <svg class="w-5 h-5 text-gray-400 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
              </svg>
              <span class="text-sm font-medium text-gray-900">Create Project</span>
            </button>

            <%= link_to property_path(@property), method: :delete,
                data: { 
                  confirm: "Are you sure you want to delete this property? This action cannot be undone and will also delete all associated projects.",
                  turbo_method: :delete 
                },
                class: "flex items-center p-3 rounded-lg hover:bg-red-50 transition-colors" do %>
              <svg class="w-5 h-5 text-red-400 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
              </svg>
              <span class="text-sm font-medium text-red-600">Delete Property</span>
            <% end %>
          </div>
        </div>

        <!-- Property Stats -->
        <div class="bg-white rounded-lg shadow">
          <div class="px-6 py-4 border-b border-gray-200">
            <h2 class="text-lg font-semibold text-gray-900">Property Stats</h2>
          </div>
          <div class="p-6 space-y-4">
            <div class="flex justify-between">
              <span class="text-sm text-gray-500">Total Projects</span>
              <span class="text-sm font-medium text-gray-900"><%= @projects.count %></span>
            </div>
            <div class="flex justify-between">
              <span class="text-sm text-gray-500">Active Projects</span>
              <span class="text-sm font-medium text-gray-900"><%= @projects.where(status: 'active').count %></span>
            </div>
            <div class="flex justify-between">
              <span class="text-sm text-gray-500">Completed Projects</span>
              <span class="text-sm font-medium text-gray-900"><%= @projects.where(status: 'completed').count %></span>
            </div>
            <div class="flex justify-between">
              <span class="text-sm text-gray-500">Added</span>
              <span class="text-sm font-medium text-gray-900"><%= @property.created_at.strftime("%B %Y") %></span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>