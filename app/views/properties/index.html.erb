<!-- Properties Index -->
<div class="min-h-screen bg-gray-50">
  <!-- Header -->
  <div class="bg-white shadow">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="flex justify-between items-center py-6">
        <div>
          <h1 class="text-3xl font-bold text-gray-900">My Properties</h1>
          <p class="text-gray-600">Manage all your properties in one place</p>
        </div>
        <div class="flex items-center space-x-4">
          <%= link_to "Back to Dashboard", homeowner_dashboard_path,
              class: "text-tradecrews-blue-600 hover:text-tradecrews-blue-500 font-medium" %>
          <%= link_to "Add New Property", new_property_path,
              class: "bg-tradecrews-orange-600 hover:bg-tradecrews-orange-700 text-white px-4 py-2 rounded-lg font-medium transition-colors" %>
        </div>
      </div>
    </div>
  </div>

  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <%= render 'shared/flash_messages' %>

    <% if @properties.any? %>
      <!-- Properties Grid -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <% @properties.each do |property| %>
          <div class="bg-white rounded-lg shadow hover:shadow-lg transition-shadow duration-200">
            <!-- Property Image -->
            <div class="aspect-w-16 aspect-h-9 bg-gray-200 rounded-t-lg overflow-hidden">
              <% if property.image.attached? %>
                <%= image_tag property.image, class: "w-full h-48 object-cover" %>
              <% else %>
                <div class="w-full h-48 bg-gray-100 flex items-center justify-center">
                  <svg class="w-12 h-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2 2v0"></path>
                  </svg>
                </div>
              <% end %>
            </div>

            <!-- Property Info -->
            <div class="p-6">
              <div class="flex items-start justify-between mb-2">
                <h3 class="text-lg font-semibold text-gray-900 truncate"><%= property.name %></h3>
                <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-tradecrews-orange-100 text-tradecrews-orange-800 ml-2">
                  <%= property.property_type.humanize %>
                </span>
              </div>

              <% if property.address %>
                <p class="text-sm text-gray-600 mb-3">
                  <%= property.address.street %><br>
                  <%= property.address.city %>, <%= property.address.state %> <%= property.address.zip_code %>
                </p>
              <% end %>

              <% if property.description.present? %>
                <p class="text-sm text-gray-500 mb-4 line-clamp-2">
                  <%= truncate(property.description, length: 100) %>
                </p>
              <% end %>

              <!-- Action Buttons -->
              <div class="flex items-center justify-between pt-4 border-t border-gray-100">
                <div class="flex space-x-2">
                  <%= link_to "View", property_path(property),
                      class: "text-sm text-tradecrews-blue-600 hover:text-tradecrews-blue-500 font-medium" %>
                  <%= link_to "Edit", edit_property_path(property),
                      class: "text-sm text-gray-600 hover:text-gray-500 font-medium" %>
                </div>
                <%= link_to "Delete", property_path(property), method: :delete,
                    data: { 
                      confirm: "Are you sure you want to delete this property? This action cannot be undone.",
                      turbo_method: :delete 
                    },
                    class: "text-sm text-red-600 hover:text-red-500 font-medium" %>
              </div>
            </div>
          </div>
        <% end %>
      </div>
    <% else %>
      <!-- Empty State -->
      <div class="text-center py-12">
        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2 2v0"></path>
        </svg>
        <h3 class="mt-2 text-sm font-medium text-gray-900">No properties</h3>
        <p class="mt-1 text-sm text-gray-500">Get started by adding your first property.</p>
        <div class="mt-6">
          <%= link_to "Add New Property", new_property_path,
              class: "inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-tradecrews-orange-600 hover:bg-tradecrews-orange-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-tradecrews-orange-500" %>
        </div>
      </div>
    <% end %>
  </div>
</div>