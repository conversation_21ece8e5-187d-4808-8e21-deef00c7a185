<p style="color: red"><%= alert %></p>

<% if Current.user.otp_required_for_sign_in? %>
  <h1>Want to replace your existing 2FA setup?</h1>

  <p>Your account is already protected with two-factor authentication. You can replace that setup if you want to switch to a new phone or authenticator app.</p>

  <p><strong>Do you want to continue? Your existing 2FA setup will no longer work.</strong></p>

  <%= button_to "Yes, replace my 2FA setup", two_factor_authentication_profile_totp_path, method: :patch %>

  <hr>
<% end %>

<h1>Upgrade your security with 2FA</h1>

<h2>Step 1: Get an Authenticator App</h2>
<p>First, you'll need a 2FA authenticator app on your phone. <strong>If you already have one, skip to step 2.</strong></p>
<p><strong>If you don't have one, or you aren't sure, we recommend Microsoft Authenticator</strong>. You can download it free on the Apple App Store for iPhone, or Google Play Store for Android. Please grab your phone, search the store, and install it now.</p>

<h2>Step 2: Scan + Enter the Code</h2>
<p>Next, open the authenticator app, tap "Scan QR code" or "+", and, when it asks, point your phone's camera at this QR code picture below.</p>

<figure>
  <%= image_tag @qr_code.as_png(resize_exactly_to: 200).to_data_url%>
  <figcaption>Point your camera here</figcaption>
</figure>

<%= form_with(url: two_factor_authentication_profile_totp_path) do |form| %>
  <div>
    <%= form.label :code, "After scanning with your camera, the app will generate a six-digit code. Enter it here:", style: "display: block" %>
    <%= form.text_field :code, required: true, autofocus: true, autocomplete: :off %>
  </div>

  <div>
    <%= form.submit "Verify and activate" %>
  </div>
<% end %>

<br>

<div>
  <%= link_to "Back", root_path %>
</div>
