<!-- Sign In Page -->
<div class="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
  <div class="sm:mx-auto sm:w-full sm:max-w-md">
    <!-- Logo -->
    <div class="flex justify-center">
      <div class="flex items-center">
        <%= image_tag "tc-logomark.webp", class: "h-12 w-12 mr-3" %>
        <h1 class="text-3xl font-bold text-tradecrews-blue-600">TradeCrews</h1>
      </div>
    </div>
    <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">
      Sign in to your account
    </h2>
    <p class="mt-2 text-center text-sm text-gray-600">
      Or
      <%= link_to "create a new account", sign_up_path, class: "font-medium text-tradecrews-orange-600 hover:text-tradecrews-orange-500" %>
    </p>
  </div>

  <div class="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
    <div class="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
      <%= render 'shared/flash_messages' %>

      <%= form_with url: sign_in_path, local: true, html: { class: "space-y-6" } do |f| %>

        <!-- Email Field -->
        <div>
          <%= f.label :email, class: "block text-sm font-medium text-gray-700" %>
          <div class="mt-1">
            <%= f.email_field :email, autofocus: true, autocomplete: "email",
                class: "appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-tradecrews-orange-500 focus:border-tradecrews-orange-500 sm:text-sm" %>
          </div>
        </div>

        <!-- Password Field -->
        <div>
          <%= f.label :password, class: "block text-sm font-medium text-gray-700" %>
          <div class="mt-1">
            <%= f.password_field :password, autocomplete: "current-password",
                class: "appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-tradecrews-orange-500 focus:border-tradecrews-orange-500 sm:text-sm" %>
          </div>
        </div>

        <!-- Remember Me (if supported in custom auth) -->
        <div class="flex items-center justify-between">
          <div class="flex items-center">
            <%= f.check_box :remember_me, class: "h-4 w-4 text-tradecrews-orange-600 focus:ring-tradecrews-orange-500 border-gray-300 rounded" %>
            <%= f.label :remember_me, class: "ml-2 block text-sm text-gray-900" %>
          </div>
          <div class="text-sm">
            <%= link_to "Forgot your password?", new_identity_password_reset_path, class: "font-medium text-tradecrews-orange-600 hover:text-tradecrews-orange-500" %>
          </div>
        </div>

        <!-- Submit Button -->
        <div>
          <%= f.submit "Sign in",
              class: "w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-tradecrews-orange-600 hover:bg-tradecrews-orange-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-tradecrews-orange-500 transition-colors duration-200" %>
        </div>

      <% end %>

      <!-- Social Login -->
      <div class="mt-6">
        <div class="relative mb-6">
          <div class="absolute inset-0 flex items-center">
            <div class="w-full border-t border-gray-300"></div>
          </div>
          <div class="relative flex justify-center text-sm">
            <span class="px-2 bg-white text-gray-500">Or continue with</span>
          </div>
        </div>

        <div class="grid grid-cols-2 gap-3">
          <%= link_to '/auth/google_oauth2', method: :post,
              class: "w-full inline-flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 transition-colors duration-200" do %>
            <svg class="w-5 h-5" viewBox="0 0 24 24">
              <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
              <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
              <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
              <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
            </svg>
            <span class="ml-2">Google</span>
          <% end %>

          <%= link_to '/auth/entra_id', method: :post,
              class: "w-full inline-flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 transition-colors duration-200" do %>
            <svg class="w-5 h-5" viewBox="0 0 24 24">
              <path fill="#00BCF2" d="M0 0h11.377v11.372H0z"/>
              <path fill="#0078D4" d="M12.623 0H24v11.372H12.623z"/>
              <path fill="#00BCF2" d="M0 12.623h11.377V24H0z"/>
              <path fill="#FFB900" d="M12.623 12.623H24V24H12.623z"/>
            </svg>
            <span class="ml-2">Microsoft</span>
          <% end %>
        </div>
      </div>

      <!-- Additional Links -->
      <div class="mt-6">
        <div class="relative mb-6">
          <div class="absolute inset-0 flex items-center">
            <div class="w-full border-t border-gray-300"></div>
          </div>
          <div class="relative flex justify-center text-sm">
            <span class="px-2 bg-white text-gray-500">New to TradeCrews?</span>
          </div>
        </div>
        <div class="text-center">
          <%= link_to "Join our community", sign_up_path,
            class: "w-full flex justify-center py-2 px-4 border border-tradecrews-blue-600 rounded-md shadow-sm text-sm font-medium text-tradecrews-blue-600 bg-white hover:bg-tradecrews-blue-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-tradecrews-blue-500 transition-colors duration-200" %>
        </div>
      </div>
    </div>
  </div>
