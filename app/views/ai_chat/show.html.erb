<div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
  <!-- Navigation Header -->
  <div class="flex items-center justify-between mb-8">
    <div class="flex items-center space-x-4">
      <%= link_to dashboard_path, class: "flex items-center text-gray-600 hover:text-gray-900 transition-colors" do %>
        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
        </svg>
        Back to Dashboard
      <% end %>
      <div class="text-gray-300">|</div>
      <div>
        <h1 class="text-2xl font-bold text-gray-900">AI Assistant</h1>
        <p class="text-gray-600">Ask me anything about your home projects</p>
      </div>
    </div>

    <!-- User Menu -->
    <div class="flex items-center space-x-4">
      <%= render 'shared/user_menu' %>
    </div>
  </div>

  <div class="bg-white rounded-lg shadow-md border border-gray-200 p-6"
       data-controller="ai-chat-full"
       data-ai-chat-full-ask-url-value="<%= ai_chat_ask_path %>"
       data-ai-chat-full-jack-image-url-value="<%= asset_path('jack.png') %>">
    <div class="flex items-center mb-6">
      <div class="w-12 h-12 bg-gradient-to-br from-blue-400 to-blue-600 rounded-full flex items-center justify-center shadow-lg">
        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
        </svg>
      </div>
      <div class="ml-4">
        <h2 class="text-xl font-semibold text-gray-900">Chat with Jack</h2>
        <p class="text-gray-600">Your personal home improvement expert</p>
      </div>
    </div>

    <!-- Chat Interface -->
    <div class="space-y-4 mb-6 max-h-96 overflow-y-auto" 
         data-ai-chat-full-target="messages">
      <div class="flex items-start space-x-3">
        <div class="w-8 h-8 bg-gradient-to-br from-blue-400 to-blue-600 rounded-full flex items-center justify-center flex-shrink-0">
          <span class="text-sm font-bold text-white">AI</span>
        </div>
        <div class="bg-gray-100 rounded-lg p-3 max-w-md">
          <p class="text-gray-800">Hello! I'm here to help with your home improvement questions. What would you like to know?</p>
        </div>
      </div>
    </div>

    <!-- Input Form -->
    <form class="space-y-4" 
          data-ai-chat-full-target="form"
          data-action="submit->ai-chat-full#sendMessage">
      <div>
        <label class="block text-sm font-medium text-gray-700 mb-2">Context (Optional)</label>
        <div class="grid grid-cols-2 gap-2">
          <select class="border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                  data-ai-chat-full-target="propertySelect">
            <option value="">Select property...</option>
            <% Current.user.properties.each do |property| %>
              <option value="<%= property.id %>"><%= property.name %></option>
            <% end %>
          </select>
          <select class="border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                  data-ai-chat-full-target="projectSelect">
            <option value="">Select project...</option>
            <!-- Projects will be populated via JavaScript -->
          </select>
        </div>
      </div>

      <div class="flex space-x-2">
        <input type="text" 
               class="flex-1 border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500" 
               placeholder="Ask your question..."
               data-ai-chat-full-target="input">
        <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-md font-medium">
          Send
        </button>
      </div>
    </form>
  </div>
</div>
