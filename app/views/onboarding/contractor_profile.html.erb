<!-- Contractor Profile Setup -->
<div class="min-h-screen bg-gray-50 py-12">
  <div class="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8">
    <!-- Progress Bar -->
    <div class="mb-8">
      <div class="flex items-center">
        <div class="flex items-center text-sm text-tradecrews-blue-600">
          <span class="flex items-center justify-center w-8 h-8 bg-tradecrews-blue-600 text-white rounded-full mr-3">1</span>
          Email Verified
        </div>
        <div class="flex-1 h-0.5 bg-tradecrews-blue-600 mx-4"></div>
        <div class="flex items-center text-sm text-tradecrews-orange-600">
          <span class="flex items-center justify-center w-8 h-8 bg-tradecrews-orange-600 text-white rounded-full mr-3">2</span>
          Setup Your Profile
        </div>
        <div class="flex-1 h-0.5 bg-gray-300 mx-4"></div>
        <div class="flex items-center text-sm text-gray-400">
          <span class="flex items-center justify-center w-8 h-8 bg-gray-300 text-gray-600 rounded-full mr-3">3</span>
          Start Getting Leads
        </div>
      </div>
    </div>

    <!-- Header -->
    <div class="text-center mb-8">
      <h1 class="text-3xl font-bold text-gray-900 mb-4">Setup Your Contractor Profile</h1>
      <p class="text-lg text-gray-600">
        Tell us about your business so homeowners can find and hire you for their projects.
      </p>
    </div>

    <!-- Form -->
    <div class="bg-white shadow-lg rounded-lg p-8">
      <%= render 'shared/flash_messages' %>
      
      <%= form_with model: @contractor, url: onboarding_contractor_profile_path, local: true, class: "space-y-6" do |f| %>
        
        <!-- Model Error Messages -->
        <% if @contractor.errors.any? %>
          <div class="rounded-md bg-red-50 p-4">
            <div class="flex">
              <div class="flex-shrink-0">
                <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                </svg>
              </div>
              <div class="ml-3">
                <h3 class="text-sm font-medium text-red-800">
                  <%= pluralize(@contractor.errors.count, "error") %> prohibited this profile from being saved:
                </h3>
                <div class="mt-2 text-sm text-red-700">
                  <ul class="list-disc pl-5 space-y-1">
                    <% @contractor.errors.full_messages.each do |message| %>
                      <li><%= message %></li>
                    <% end %>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        <% end %>

        <!-- Company Information -->
        <div class="border-b border-gray-200 pb-6">
          <h3 class="text-lg font-medium text-gray-900 mb-4">Company Information</h3>
          
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- Company Name -->
            <div>
              <%= f.label :company_name, "Company Name", class: "block text-sm font-medium text-gray-700 mb-2" %>
              <%= f.text_field :company_name, 
                  class: "block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-tradecrews-orange-500 focus:border-tradecrews-orange-500",
                  placeholder: "Your Company Name" %>
            </div>

            <!-- Phone -->
            <div>
              <%= f.label :phone, "Business Phone", class: "block text-sm font-medium text-gray-700 mb-2" %>
              <%= f.telephone_field :phone, 
                  class: "block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-tradecrews-orange-500 focus:border-tradecrews-orange-500",
                  placeholder: "(*************" %>
            </div>
          </div>

          <!-- Website -->
          <div class="mt-4">
            <%= f.label :website, "Website (Optional)", class: "block text-sm font-medium text-gray-700 mb-2" %>
            <%= f.url_field :website, 
                class: "block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-tradecrews-orange-500 focus:border-tradecrews-orange-500",
                placeholder: "https://yourcompany.com" %>
          </div>
        </div>

        <!-- Professional Details -->
        <div class="border-b border-gray-200 pb-6">
          <h3 class="text-lg font-medium text-gray-900 mb-4">Professional Details</h3>
          
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- Years of Experience -->
            <div>
              <%= f.label :years_experience, "Years of Experience", class: "block text-sm font-medium text-gray-700 mb-2" %>
              <%= f.number_field :years_experience, 
                  class: "block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-tradecrews-orange-500 focus:border-tradecrews-orange-500",
                  min: 0, max: 50 %>
            </div>

            <!-- License Number -->
            <div>
              <%= f.label :license_number, "License Number (Optional)", class: "block text-sm font-medium text-gray-700 mb-2" %>
              <%= f.text_field :license_number, 
                  class: "block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-tradecrews-orange-500 focus:border-tradecrews-orange-500",
                  placeholder: "License #" %>
            </div>
          </div>

          <!-- Insurance -->
          <div class="mt-4">
            <div class="flex items-start">
              <div class="flex items-center h-5">
                <%= f.check_box :insurance_verified, class: "h-4 w-4 text-tradecrews-orange-600 focus:ring-tradecrews-orange-500 border-gray-300 rounded" %>
              </div>
              <div class="ml-3 text-sm">
                <%= f.label :insurance_verified, "I have current liability insurance", class: "font-medium text-gray-700" %>
                <p class="text-gray-500">Homeowners prefer contractors with verified insurance</p>
              </div>
            </div>
          </div>
        </div>

        <!-- Trades/Specializations -->
        <div class="border-b border-gray-200 pb-6">
          <h3 class="text-lg font-medium text-gray-900 mb-4">Your Specializations</h3>
          <p class="text-sm text-gray-600 mb-4">Select the trades and services you offer (you can add more later)</p>
          
          <div class="grid grid-cols-2 md:grid-cols-3 gap-4">
            <% Trade.all.each do |trade| %>
              <div class="relative">
                <input type="checkbox" name="contractor[trade_ids][]" value="<%= trade.id %>" id="trade_<%= trade.id %>" class="sr-only peer">
                <label for="trade_<%= trade.id %>" class="flex items-center p-3 border border-gray-300 rounded-lg cursor-pointer hover:bg-gray-50 peer-checked:border-tradecrews-orange-500 peer-checked:bg-tradecrews-orange-50 peer-checked:text-tradecrews-orange-600 transition-colors duration-200">
                  <span class="text-sm font-medium"><%= trade.name %></span>
                </label>
              </div>
            <% end %>
          </div>
        </div>

        <!-- Description -->
        <div>
          <%= f.label :description, "About Your Business", class: "block text-sm font-medium text-gray-700 mb-2" %>
          <%= f.text_area :description, rows: 4,
              class: "block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-tradecrews-orange-500 focus:border-tradecrews-orange-500",
              placeholder: "Tell homeowners about your business, experience, and what makes you stand out..." %>
          <p class="mt-1 text-sm text-gray-500">This will be displayed on your public profile</p>
        </div>

        <!-- Submit Button -->
        <div class="flex justify-end pt-6">
          <%= f.submit "Complete Profile & Start Getting Leads", 
              class: "bg-tradecrews-orange-600 hover:bg-tradecrews-orange-700 text-white px-8 py-3 rounded-lg font-semibold text-lg transition-colors duration-200" %>
        </div>

      <% end %>
    </div>

    <!-- Help Text -->
    <div class="mt-8 text-center">
      <p class="text-sm text-gray-500">
        You can update your profile information anytime from your contractor dashboard.
      </p>
    </div>
  </div>
</div>