<!-- Homeowner Property Setup -->
<div class="min-h-screen bg-gray-50 py-12">
  <div class="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8">
    <!-- Progress Bar -->
    <div class="mb-8">
      <div class="flex items-center">
        <div class="flex items-center text-sm text-tradecrews-blue-600">
          <span class="flex items-center justify-center w-8 h-8 bg-tradecrews-blue-600 text-white rounded-full mr-3">1</span>
          Email Verified
        </div>
        <div class="flex-1 h-0.5 bg-tradecrews-blue-600 mx-4"></div>
        <div class="flex items-center text-sm text-tradecrews-orange-600">
          <span class="flex items-center justify-center w-8 h-8 bg-tradecrews-orange-600 text-white rounded-full mr-3">2</span>
          Add Your Property
        </div>
        <div class="flex-1 h-0.5 bg-gray-300 mx-4"></div>
        <div class="flex items-center text-sm text-gray-400">
          <span class="flex items-center justify-center w-8 h-8 bg-gray-300 text-gray-600 rounded-full mr-3">3</span>
          Start Finding Contractors
        </div>
      </div>
    </div>

    <!-- Header -->
    <div class="text-center mb-8">
      <h1 class="text-3xl font-bold text-gray-900 mb-4">Add Your First Property</h1>
      <p class="text-lg text-gray-600">
        Let's start by adding the property where you need work done. You can add more properties later.
      </p>
    </div>

    <!-- Form -->
    <div class="bg-white shadow-lg rounded-lg p-8">
      <%= render 'shared/flash_messages' %>
      
      <%= form_with model: @property, url: onboarding_property_path, local: true, class: "space-y-6" do |f| %>

        <!-- Model Error Messages -->
        <% if @property.errors.any? %>
          <div class="rounded-md bg-red-50 p-4">
            <div class="flex">
              <div class="flex-shrink-0">
                <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                </svg>
              </div>
              <div class="ml-3">
                <h3 class="text-sm font-medium text-red-800">
                  <%= pluralize(@property.errors.count, "error") %> prohibited this property from being saved:
                </h3>
                <div class="mt-2 text-sm text-red-700">
                  <ul class="list-disc pl-5 space-y-1">
                    <% @property.errors.full_messages.each do |message| %>
                      <li><%= message %></li>
                    <% end %>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        <% end %>

        <!-- Property Name -->
        <div>
          <%= f.label :name, "Property Name", class: "block text-sm font-medium text-gray-700 mb-2" %>
          <%= f.text_field :name,
              class: "block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-tradecrews-orange-500 focus:border-tradecrews-orange-500",
              placeholder: "e.g., Main House, Rental Property, Office Building" %>
          <p class="mt-1 text-sm text-gray-500">Give your property a name to easily identify it</p>
        </div>

        <!-- Property Type -->
        <div>
          <%= f.label :property_type, "Property Type", class: "block text-sm font-medium text-gray-700 mb-2" %>
          <%= f.select :property_type,
              options_for_select([
                ['Single Family Home', 'single_family'],
                ['Condominium', 'condo'],
                ['Townhouse', 'townhouse'],
                ['Multi Family', 'multi_family'],
                ['Mobile Home', 'mobile_home'],
                ['Other', 'other']
              ]),
              { prompt: 'Select property type' },
              { class: "block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-tradecrews-orange-500 focus:border-tradecrews-orange-500" } %>
        </div>

        <!-- Address Section -->
        <div class="border-t border-gray-200 pt-6">
          <h3 class="text-lg font-medium text-gray-900 mb-4">Property Address</h3>

          <%= f.fields_for :address do |address_form| %>
            <!-- Street Address -->
            <div class="mb-4">
              <%= address_form.label :street, "Street Address", class: "block text-sm font-medium text-gray-700 mb-2" %>
              <%= address_form.text_field :street,
                  class: "block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-tradecrews-orange-500 focus:border-tradecrews-orange-500",
                  placeholder: "123 Main Street" %>
            </div>

            <!-- City, State, Zip -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <%= address_form.label :city, "City", class: "block text-sm font-medium text-gray-700 mb-2" %>
                <%= address_form.text_field :city,
                    class: "block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-tradecrews-orange-500 focus:border-tradecrews-orange-500" %>
              </div>
              <div>
                <%= address_form.label :state, "State", class: "block text-sm font-medium text-gray-700 mb-2" %>
                <%= address_form.text_field :state,
                    class: "block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-tradecrews-orange-500 focus:border-tradecrews-orange-500" %>
              </div>
              <div>
                <%= address_form.label :zip_code, "ZIP Code", class: "block text-sm font-medium text-gray-700 mb-2" %>
                <%= address_form.text_field :zip_code,
                    class: "block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-tradecrews-orange-500 focus:border-tradecrews-orange-500" %>
              </div>
            </div>
          <% end %>
        </div>

        <!-- Property Image Upload -->
        <div class="border-t border-gray-200 pt-6">
          <h3 class="text-lg font-medium text-gray-900 mb-4">Property Photo (Optional)</h3>
          <p class="text-sm text-gray-600 mb-4">Add a photo to help contractors better understand your property</p>

          <div class="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-md hover:border-tradecrews-orange-400 transition-colors duration-200" id="drop-zone" data-controller="property-image" data-property-image-target="dropZone">
            <div class="space-y-1 text-center">
              <svg class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48" aria-hidden="true">
                <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
              </svg>
              <div class="flex text-sm text-gray-600">
                <label for="property_image" class="relative cursor-pointer bg-white rounded-md font-medium text-tradecrews-orange-600 hover:text-tradecrews-orange-500 focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-tradecrews-orange-500">
                  <span>Upload a file</span>
                  <%= f.file_field :image, id: "property_image", class: "sr-only", accept: "image/*", data: { 'property-image-target': 'fileInput' } %>
                </label>
                <p class="pl-1">or drag and drop</p>
              </div>
              <p class="text-xs text-gray-500">PNG, JPG, GIF up to 10MB</p>
            </div>
          </div>

          <!-- Image Preview -->
          <div id="image-preview" class="mt-4 hidden" data-property-image-target="imagePreview">
            <div class="relative">
              <img id="preview-image" class="h-32 w-32 object-cover rounded-lg border border-gray-300" src="" alt="Property preview" data-property-image-target="previewImage">
              <button type="button" id="remove-image" class="absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600 transition-colors duration-200" data-property-image-target="removeButton">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
              </button>
            </div>
          </div>
        </div>

        <!-- Description -->
        <div>
          <%= f.label :description, "Property Description (Optional)", class: "block text-sm font-medium text-gray-700 mb-2" %>
          <%= f.text_area :description, rows: 4,
              class: "block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-tradecrews-orange-500 focus:border-tradecrews-orange-500",
              placeholder: "Describe your property, any special features, or specific areas where you might need work done..." %>
        </div>

        <!-- Submit Button -->
        <div class="flex justify-end pt-6">
          <%= f.submit "Add Property & Complete Setup",
              class: "bg-tradecrews-orange-600 hover:bg-tradecrews-orange-700 text-white px-8 py-3 rounded-lg font-semibold text-lg transition-colors duration-200" %>
        </div>

      <% end %>
    </div>

    <!-- Help Text -->
    <div class="mt-8 text-center">
      <p class="text-sm text-gray-500">
        Don't worry, you can edit this information or add more properties later from your dashboard.
      </p>
    </div>
  </div>
</div>
