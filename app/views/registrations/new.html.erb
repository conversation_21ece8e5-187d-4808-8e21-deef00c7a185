<!-- Sign Up Page -->
<div class="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
  <div class="sm:mx-auto sm:w-full sm:max-w-md">
    <!-- Logo -->
    <div class="flex justify-center">
      <div class="flex items-center">
        <%= image_tag "tc-logomark.webp", class: "h-12 w-12 mr-3" %>
        <h1 class="text-3xl font-bold text-tradecrews-blue-600">TradeCrews</h1>
      </div>
    </div>
    <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">
      <% if @role == 'contractor' %>
        Join as a Contractor
      <% else %>
        Join as a Homeowner
      <% end %>
    </h2>
    <p class="mt-2 text-center text-sm text-gray-600">
      Already have an account?
      <%= link_to "Sign in here", sign_in_path, class: "font-medium text-tradecrews-orange-600 hover:text-tradecrews-orange-500" %>
    </p>
  </div>

  <div class="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
    <div class="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
      <%= form_with url: sign_up_path, scope: :user, local: true, html: { class: "space-y-6" } do |f| %>

        <!-- Hidden role field -->
        <%= f.hidden_field :role, value: @role %>

        <%= render 'shared/flash_messages' %>
        
        <!-- Model Error Messages -->
        <% if @user && @user.errors.any? %>
          <div class="mb-4 rounded-md bg-red-50 p-4 border border-red-200">
            <div class="flex">
              <div class="flex-shrink-0">
                <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                </svg>
              </div>
              <div class="ml-3">
                <h3 class="text-sm font-medium text-red-800">
                  <%= pluralize(@user.errors.count, "error") %> prohibited this account from being created:
                </h3>
                <div class="mt-2 text-sm text-red-700">
                  <ul class="list-disc pl-5 space-y-1">
                    <% @user.errors.full_messages.each do |msg| %>
                      <li><%= msg %></li>
                    <% end %>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        <% end %>

        <!-- Name Field -->
        <div>
          <%= f.label :name, class: "block text-sm font-medium text-gray-700" %>
          <div class="mt-1">
            <%= f.text_field :name, autofocus: true, autocomplete: "name",
                class: "appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-tradecrews-orange-500 focus:border-tradecrews-orange-500 sm:text-sm",
                placeholder: "Your full name" %>
          </div>
        </div>

        <!-- Email Field -->
        <div>
          <%= f.label :email, class: "block text-sm font-medium text-gray-700" %>
          <div class="mt-1">
            <%= f.email_field :email, autocomplete: "email",
                class: "appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-tradecrews-orange-500 focus:border-tradecrews-orange-500 sm:text-sm",
                placeholder: "<EMAIL>" %>
          </div>
        </div>

        <!-- Password Field -->
        <div>
          <%= f.label :password, class: "block text-sm font-medium text-gray-700" %>
          <div class="mt-1">
            <%= f.password_field :password, autocomplete: "new-password",
                class: "appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-tradecrews-orange-500 focus:border-tradecrews-orange-500 sm:text-sm" %>
          </div>
        </div>

        <!-- Password Confirmation Field -->
        <div>
          <%= f.label :password_confirmation, class: "block text-sm font-medium text-gray-700" %>
          <div class="mt-1">
            <%= f.password_field :password_confirmation, autocomplete: "new-password",
                class: "appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-tradecrews-orange-500 focus:border-tradecrews-orange-500 sm:text-sm" %>
          </div>
        </div>

        <!-- Submit Button -->
        <div>
          <%= f.submit "Create Account",
              class: "w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-tradecrews-orange-600 hover:bg-tradecrews-orange-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-tradecrews-orange-500 transition-colors duration-200" %>
        </div>

      <% end %>

      <!-- Social Registration -->
      <div class="mt-6">
        <div class="relative mb-6">
          <div class="absolute inset-0 flex items-center">
            <div class="w-full border-t border-gray-300"></div>
          </div>
          <div class="relative flex justify-center text-sm">
            <span class="px-2 bg-white text-gray-500">Or sign up with</span>
          </div>
        </div>

        <div class="grid grid-cols-2 gap-3">
          <%= link_to '/auth/google_oauth2', method: :post,
              class: "w-full inline-flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 transition-colors duration-200" do %>
            <svg class="w-5 h-5" viewBox="0 0 24 24">
              <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
              <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
              <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
              <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
            </svg>
            <span class="ml-2">Google</span>
          <% end %>

          <%= link_to '/auth/entra_id', method: :post,
              class: "w-full inline-flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 transition-colors duration-200" do %>
            <svg class="w-5 h-5" viewBox="0 0 24 24">
              <path fill="#00BCF2" d="M0 0h11.377v11.372H0z"/>
              <path fill="#0078D4" d="M12.623 0H24v11.372H12.623z"/>
              <path fill="#00BCF2" d="M0 12.623h11.377V24H0z"/>
              <path fill="#FFB900" d="M12.623 12.623H24V24H12.623z"/>
            </svg>
            <span class="ml-2">Microsoft</span>
          <% end %>
        </div>
      </div>
    </div>
  </div>

  <!-- Why Join TradeCrews Section - Separate card below the form -->
  <div class="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
    <div class="bg-white py-6 px-4 shadow sm:rounded-lg sm:px-10">
      <div class="text-center mb-4">
        <h3 class="text-lg font-medium text-gray-900">
          <% if @role == 'contractor' %>
            Why contractors choose TradeCrews
          <% else %>
            Why homeowners choose TradeCrews
          <% end %>
        </h3>
      </div>

      <div class="space-y-3">
        <% if @role == 'contractor' %>
          <div class="flex items-center text-sm text-gray-600">
            <svg class="w-4 h-4 text-green-500 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
            </svg>
            Get paid faster with secure payments
          </div>
          <div class="flex items-center text-sm text-gray-600">
            <svg class="w-4 h-4 text-green-500 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2-2v2m8 0V6a2 2 0 012 2v6a2 2 0 01-2 2H8a2 2 0 01-2-2V8a2 2 0 012-2h8z"></path>
            </svg>
            Access to quality leads in your area
          </div>
          <div class="flex items-center text-sm text-gray-600">
            <svg class="w-4 h-4 text-green-500 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            Build your reputation with reviews
          </div>
        <% else %>
          <div class="flex items-center text-sm text-gray-600">
            <svg class="w-4 h-4 text-green-500 mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
            </svg>
            Connect with verified professionals
          </div>
          <div class="flex items-center text-sm text-gray-600">
            <svg class="w-4 h-4 text-green-500 mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
            </svg>
            Secure payment protection
          </div>
          <div class="flex items-center text-sm text-gray-600">
            <svg class="w-4 h-4 text-green-500 mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
            </svg>
            24/7 customer support
          </div>
        <% end %>
      </div>
    </div>
  </div>
</div>
