<!-- Profile Page -->
<div class="min-h-screen bg-gray-50">
  <!-- Header -->
  <div class="bg-white shadow">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="flex justify-between items-center py-6">
        <div>
          <h1 class="text-3xl font-bold text-gray-900">Profile Settings</h1>
          <p class="text-gray-600">Manage your account settings and preferences</p>
        </div>
        <div class="flex items-center space-x-4">
          <%= link_to "Back to Dashboard", 
              Current.user.contractor? ? contractor_dashboard_path : homeowner_dashboard_path,
              class: "text-tradecrews-blue-600 hover:text-tradecrews-blue-500 font-medium" %>
          <%= link_to "Edit Profile", edit_profile_path,
              class: "bg-tradecrews-orange-600 hover:bg-tradecrews-orange-700 text-white px-4 py-2 rounded-lg font-medium transition-colors" %>
        </div>
      </div>
    </div>
  </div>

  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <%= render 'shared/flash_messages' %>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
      <!-- Main Profile Information -->
      <div class="lg:col-span-2 space-y-8">
        <!-- Basic Information Card -->
        <div class="bg-white rounded-lg shadow">
          <div class="px-6 py-4 border-b border-gray-200">
            <h2 class="text-lg font-semibold text-gray-900">Basic Information</h2>
          </div>
          <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Full Name</label>
                <p class="text-sm text-gray-900"><%= @user.name %></p>
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Email Address</label>
                <div class="flex items-center space-x-2">
                  <p class="text-sm text-gray-900"><%= @user.email %></p>
                  <% unless @user.verified? %>
                    <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-yellow-100 text-yellow-800">
                      Unverified
                    </span>
                  <% end %>
                </div>
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Account Type</label>
                <p class="text-sm text-gray-900 capitalize"><%= @user.role %></p>
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Member Since</label>
                <p class="text-sm text-gray-900"><%= @user.created_at.strftime("%B %Y") %></p>
              </div>
            </div>
          </div>
        </div>

        <!-- Contractor Profile Card (if contractor) -->
        <% if @contractor %>
          <div class="bg-white rounded-lg shadow">
            <div class="px-6 py-4 border-b border-gray-200">
              <h2 class="text-lg font-semibold text-gray-900">Contractor Profile</h2>
            </div>
            <div class="p-6">
              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">Company Name</label>
                  <p class="text-sm text-gray-900"><%= @contractor.company_name || "Not specified" %></p>
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">Phone Number</label>
                  <p class="text-sm text-gray-900"><%= @contractor.phone || "Not specified" %></p>
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">Years of Experience</label>
                  <p class="text-sm text-gray-900"><%= @contractor.years_experience || "Not specified" %></p>
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">License Number</label>
                  <p class="text-sm text-gray-900"><%= @contractor.license_number || "Not specified" %></p>
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">Website</label>
                  <% if @contractor.website.present? %>
                    <%= link_to @contractor.website, @contractor.website, 
                        class: "text-sm text-tradecrews-blue-600 hover:text-tradecrews-blue-500",
                        target: "_blank", rel: "noopener" %>
                  <% else %>
                    <p class="text-sm text-gray-900">Not specified</p>
                  <% end %>
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">Insurance Status</label>
                  <div class="flex items-center">
                    <% if @contractor.insurance_verified? %>
                      <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800">
                        <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                          <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        Verified
                      </span>
                    <% else %>
                      <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-800">
                        Not verified
                      </span>
                    <% end %>
                  </div>
                </div>
              </div>
              
              <% if @contractor.description.present? %>
                <div class="mt-6">
                  <label class="block text-sm font-medium text-gray-700 mb-1">About</label>
                  <p class="text-sm text-gray-900"><%= @contractor.description %></p>
                </div>
              <% end %>

              <% if @contractor.trades.any? %>
                <div class="mt-6">
                  <label class="block text-sm font-medium text-gray-700 mb-2">Specializations</label>
                  <div class="flex flex-wrap gap-2">
                    <% @contractor.trades.each do |trade| %>
                      <span class="inline-flex items-center px-3 py-0.5 rounded-full text-sm font-medium bg-tradecrews-orange-100 text-tradecrews-orange-800">
                        <%= trade.name %>
                      </span>
                    <% end %>
                  </div>
                </div>
              <% end %>
            </div>
          </div>
        <% end %>

        <!-- Security Settings Card -->
        <div class="bg-white rounded-lg shadow">
          <div class="px-6 py-4 border-b border-gray-200">
            <h2 class="text-lg font-semibold text-gray-900">Security Settings</h2>
          </div>
          <div class="p-6 space-y-4">
            <div class="flex items-center justify-between py-3 border-b border-gray-100">
              <div>
                <h3 class="text-sm font-medium text-gray-900">Password</h3>
                <p class="text-sm text-gray-500">Last changed recently</p>
              </div>
              <%= link_to "Change Password", edit_password_path,
                  class: "text-sm text-tradecrews-blue-600 hover:text-tradecrews-blue-500 font-medium" %>
            </div>

            <div class="flex items-center justify-between py-3 border-b border-gray-100">
              <div>
                <h3 class="text-sm font-medium text-gray-900">Two-Factor Authentication</h3>
                <p class="text-sm text-gray-500">
                  <% if @user.otp_required_for_sign_in? %>
                    Add an extra layer of security to your account
                  <% else %>
                    Protect your account with 2FA
                  <% end %>
                </p>
              </div>
              <div class="flex items-center space-x-3">
                <% if @user.otp_required_for_sign_in? %>
                  <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800">
                    Enabled
                  </span>
                  <%= link_to "Manage", two_factor_authentication_profile_recovery_codes_path,
                      class: "text-sm text-tradecrews-blue-600 hover:text-tradecrews-blue-500 font-medium" %>
                <% else %>
                  <%= link_to "Enable", new_two_factor_authentication_profile_totp_path,
                      class: "text-sm text-tradecrews-blue-600 hover:text-tradecrews-blue-500 font-medium" %>
                <% end %>
              </div>
            </div>

            <div class="flex items-center justify-between py-3">
              <div>
                <h3 class="text-sm font-medium text-gray-900">Active Sessions</h3>
                <p class="text-sm text-gray-500">Manage your active login sessions</p>
              </div>
              <%= link_to "View All", sessions_path,
                  class: "text-sm text-tradecrews-blue-600 hover:text-tradecrews-blue-500 font-medium" %>
            </div>
          </div>
        </div>
      </div>

      <!-- Sidebar -->
      <div class="space-y-6">
        <!-- Quick Actions Card -->
        <div class="bg-white rounded-lg shadow">
          <div class="px-6 py-4 border-b border-gray-200">
            <h2 class="text-lg font-semibold text-gray-900">Quick Actions</h2>
          </div>
          <div class="p-6 space-y-3">
            <%= link_to edit_profile_path,
                class: "flex items-center p-3 rounded-lg hover:bg-gray-50 transition-colors" do %>
              <svg class="w-5 h-5 text-gray-400 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
              </svg>
              <span class="text-sm font-medium text-gray-900">Edit Profile</span>
            <% end %>

            <% unless @user.verified? %>
              <%= link_to identity_email_verification_path, method: :post,
                  class: "flex items-center p-3 rounded-lg hover:bg-gray-50 transition-colors" do %>
                <svg class="w-5 h-5 text-gray-400 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                </svg>
                <span class="text-sm font-medium text-gray-900">Verify Email</span>
              <% end %>
            <% end %>

            <%= link_to edit_password_path,
                class: "flex items-center p-3 rounded-lg hover:bg-gray-50 transition-colors" do %>
              <svg class="w-5 h-5 text-gray-400 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
              </svg>
              <span class="text-sm font-medium text-gray-900">Change Password</span>
            <% end %>
          </div>
        </div>

        <!-- Recent Sessions Card -->
        <div class="bg-white rounded-lg shadow">
          <div class="px-6 py-4 border-b border-gray-200">
            <h2 class="text-lg font-semibold text-gray-900">Recent Sessions</h2>
          </div>
          <div class="p-6">
            <% if @sessions.any? %>
              <div class="space-y-3">
                <% @sessions.each do |session| %>
                  <div class="flex items-center justify-between py-2">
                    <div>
                      <p class="text-sm font-medium text-gray-900">
                        <% if session == Current.session %>
                          Current session
                        <% else %>
                          Session
                        <% end %>
                      </p>
                      <p class="text-xs text-gray-500">
                        <%= time_ago_in_words(session.created_at) %> ago
                      </p>
                    </div>
                    <% if session == Current.session %>
                      <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800">
                        Active
                      </span>
                    <% end %>
                  </div>
                <% end %>
              </div>
              <div class="mt-4 pt-4 border-t border-gray-100">
                <%= link_to "View All Sessions", sessions_path,
                    class: "text-sm text-tradecrews-blue-600 hover:text-tradecrews-blue-500 font-medium" %>
              </div>
            <% else %>
              <p class="text-sm text-gray-500">No recent sessions</p>
            <% end %>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>