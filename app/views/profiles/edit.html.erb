<!-- Edit Profile Page -->
<div class="min-h-screen bg-gray-50">
  <!-- Header -->
  <div class="bg-white shadow">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="flex justify-between items-center py-6">
        <div>
          <h1 class="text-3xl font-bold text-gray-900">Edit Profile</h1>
          <p class="text-gray-600">Update your account information and preferences</p>
        </div>
        <div class="flex items-center space-x-4">
          <%= link_to "Back to Profile", profile_path,
              class: "text-tradecrews-blue-600 hover:text-tradecrews-blue-500 font-medium" %>
        </div>
      </div>
    </div>
  </div>

  <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <%= render 'shared/flash_messages' %>

    <div class="space-y-8">
      <!-- Basic Information Section -->
      <div class="bg-white rounded-lg shadow">
        <div class="px-6 py-4 border-b border-gray-200">
          <h2 class="text-lg font-semibold text-gray-900">Basic Information</h2>
          <p class="text-sm text-gray-600">Update your personal details</p>
        </div>
        
        <%= form_with model: @user, url: profile_path, method: :patch, local: true, class: "p-6" do |form| %>
          <!-- Model Error Messages -->
          <% if @user.errors.any? %>
            <div class="mb-6 rounded-md bg-red-50 p-4 border border-red-200">
              <div class="flex">
                <div class="flex-shrink-0">
                  <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                  </svg>
                </div>
                <div class="ml-3">
                  <h3 class="text-sm font-medium text-red-800">
                    <%= pluralize(@user.errors.count, "error") %> prohibited this profile from being saved:
                  </h3>
                  <div class="mt-2 text-sm text-red-700">
                    <ul class="list-disc pl-5 space-y-1">
                      <% @user.errors.each do |error| %>
                        <li><%= error.full_message %></li>
                      <% end %>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          <% end %>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <%= form.label :name, "Full Name", class: "block text-sm font-medium text-gray-700 mb-1" %>
              <%= form.text_field :name, required: true,
                  class: "w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-tradecrews-orange-500 focus:border-tradecrews-orange-500" %>
            </div>

            <div>
              <%= form.label :email, "Email Address", class: "block text-sm font-medium text-gray-700 mb-1" %>
              <div class="flex items-center space-x-2">
                <%= form.email_field :email, required: true,
                    class: "flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-tradecrews-orange-500 focus:border-tradecrews-orange-500" %>
                <% unless @user.verified? %>
                  <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-yellow-100 text-yellow-800">
                    Unverified
                  </span>
                <% end %>
              </div>
              <% unless @user.verified? %>
                <p class="mt-1 text-xs text-gray-500">
                  Changing your email will require verification
                </p>
              <% end %>
            </div>
          </div>

          <div class="mt-6 flex justify-end">
            <%= form.submit "Save Basic Information",
                class: "bg-tradecrews-orange-600 hover:bg-tradecrews-orange-700 text-white px-6 py-2 rounded-lg font-medium transition-colors" %>
          </div>
        <% end %>
      </div>

      <!-- Contractor Profile Section (if contractor) -->
      <% if @user.contractor? %>
        <div class="bg-white rounded-lg shadow">
          <div class="px-6 py-4 border-b border-gray-200">
            <h2 class="text-lg font-semibold text-gray-900">Contractor Profile</h2>
            <p class="text-sm text-gray-600">Update your business information and specializations</p>
          </div>
          
          <%= form_with model: @contractor, url: update_contractor_profile_path, method: :patch, local: true, class: "p-6" do |form| %>
            <!-- Model Error Messages -->
            <% if @contractor&.errors&.any? %>
              <div class="mb-6 rounded-md bg-red-50 p-4 border border-red-200">
                <div class="flex">
                  <div class="flex-shrink-0">
                    <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                      <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                    </svg>
                  </div>
                  <div class="ml-3">
                    <h3 class="text-sm font-medium text-red-800">
                      <%= pluralize(@contractor.errors.count, "error") %> prohibited this contractor profile from being saved:
                    </h3>
                    <div class="mt-2 text-sm text-red-700">
                      <ul class="list-disc pl-5 space-y-1">
                        <% @contractor.errors.each do |error| %>
                          <li><%= error.full_message %></li>
                        <% end %>
                      </ul>
                    </div>
                  </div>
                </div>
              </div>
            <% end %>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <%= form.label :company_name, "Company Name", class: "block text-sm font-medium text-gray-700 mb-1" %>
                <%= form.text_field :company_name,
                    class: "w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-tradecrews-orange-500 focus:border-tradecrews-orange-500" %>
              </div>

              <div>
                <%= form.label :phone, "Phone Number", class: "block text-sm font-medium text-gray-700 mb-1" %>
                <%= form.telephone_field :phone,
                    class: "w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-tradecrews-orange-500 focus:border-tradecrews-orange-500" %>
              </div>

              <div>
                <%= form.label :years_experience, "Years of Experience", class: "block text-sm font-medium text-gray-700 mb-1" %>
                <%= form.number_field :years_experience, min: 0,
                    class: "w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-tradecrews-orange-500 focus:border-tradecrews-orange-500" %>
              </div>

              <div>
                <%= form.label :license_number, "License Number", class: "block text-sm font-medium text-gray-700 mb-1" %>
                <%= form.text_field :license_number,
                    class: "w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-tradecrews-orange-500 focus:border-tradecrews-orange-500" %>
              </div>

              <div class="md:col-span-2">
                <%= form.label :website, "Website", class: "block text-sm font-medium text-gray-700 mb-1" %>
                <%= form.url_field :website, placeholder: "https://",
                    class: "w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-tradecrews-orange-500 focus:border-tradecrews-orange-500" %>
              </div>

              <div class="md:col-span-2">
                <%= form.label :trade_ids, "Specializations", class: "block text-sm font-medium text-gray-700 mb-2" %>
                <div class="grid grid-cols-2 md:grid-cols-3 gap-3">
                  <% Trade.all.each do |trade| %>
                    <label class="flex items-center">
                      <%= form.check_box :trade_ids, { multiple: true, checked: @contractor&.trade_ids&.include?(trade.id) }, trade.id, nil %>
                      <span class="ml-2 text-sm text-gray-700"><%= trade.name %></span>
                    </label>
                  <% end %>
                </div>
              </div>

              <div class="md:col-span-2">
                <%= form.label :description, "About Your Business", class: "block text-sm font-medium text-gray-700 mb-1" %>
                <%= form.text_area :description, rows: 4, placeholder: "Tell potential clients about your business, experience, and what sets you apart...",
                    class: "w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-tradecrews-orange-500 focus:border-tradecrews-orange-500" %>
              </div>

              <div class="md:col-span-2">
                <div class="flex items-center">
                  <%= form.check_box :insurance_verified, class: "h-4 w-4 text-tradecrews-orange-600 focus:ring-tradecrews-orange-500 border-gray-300 rounded" %>
                  <%= form.label :insurance_verified, "I have valid business insurance", class: "ml-2 block text-sm text-gray-700" %>
                </div>
              </div>
            </div>

            <div class="mt-6 flex justify-end">
              <%= form.submit "Save Contractor Profile",
                  class: "bg-tradecrews-orange-600 hover:bg-tradecrews-orange-700 text-white px-6 py-2 rounded-lg font-medium transition-colors" %>
            </div>
          <% end %>
        </div>
      <% end %>

      <!-- Security Actions Section -->
      <div class="bg-white rounded-lg shadow">
        <div class="px-6 py-4 border-b border-gray-200">
          <h2 class="text-lg font-semibold text-gray-900">Security Settings</h2>
          <p class="text-sm text-gray-600">Manage your account security</p>
        </div>
        <div class="p-6 space-y-4">
          <div class="flex items-center justify-between py-3 border-b border-gray-100">
            <div>
              <h3 class="text-sm font-medium text-gray-900">Password</h3>
              <p class="text-sm text-gray-500">Change your account password</p>
            </div>
            <%= link_to "Change Password", edit_password_path,
                class: "bg-gray-100 hover:bg-gray-200 text-gray-700 px-4 py-2 rounded-lg font-medium transition-colors" %>
          </div>

          <div class="flex items-center justify-between py-3 border-b border-gray-100">
            <div>
              <h3 class="text-sm font-medium text-gray-900">Two-Factor Authentication</h3>
              <p class="text-sm text-gray-500">
                <% if @user.otp_required_for_sign_in? %>
                  Manage your 2FA settings and recovery codes
                <% else %>
                  Add an extra layer of security to your account
                <% end %>
              </p>
            </div>
            <div class="flex items-center space-x-3">
              <% if @user.otp_required_for_sign_in? %>
                <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800">
                  Enabled
                </span>
                <%= link_to "Manage", two_factor_authentication_profile_recovery_codes_path,
                    class: "bg-gray-100 hover:bg-gray-200 text-gray-700 px-4 py-2 rounded-lg font-medium transition-colors" %>
              <% else %>
                <%= link_to "Enable", new_two_factor_authentication_profile_totp_path,
                    class: "bg-tradecrews-orange-600 hover:bg-tradecrews-orange-700 text-white px-4 py-2 rounded-lg font-medium transition-colors" %>
              <% end %>
            </div>
          </div>

          <div class="flex items-center justify-between py-3">
            <div>
              <h3 class="text-sm font-medium text-gray-900">Active Sessions</h3>
              <p class="text-sm text-gray-500">View and manage your login sessions</p>
            </div>
            <%= link_to "View Sessions", sessions_path,
                class: "bg-gray-100 hover:bg-gray-200 text-gray-700 px-4 py-2 rounded-lg font-medium transition-colors" %>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>