<div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
  <!-- Navigation Header -->
  <div class="flex items-center justify-between mb-8">
    <div class="flex items-center space-x-4">
      <%= link_to dashboard_path, class: "flex items-center text-gray-600 hover:text-gray-900 transition-colors" do %>
        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
        </svg>
        Back to Dashboard
      <% end %>
      <div class="text-gray-300">|</div>
      <div>
        <h1 class="text-3xl font-bold text-gray-900">AI Project Planner</h1>
        <p class="text-gray-600">Chat with Jack to plan your home improvement project step by step</p>
      </div>
    </div>

    <!-- User Menu -->
    <div class="flex items-center space-x-4">
      <%= render 'shared/user_menu' %>
    </div>
  </div>

  <div class="bg-white rounded-lg shadow-md border border-gray-200 h-[700px] flex flex-col"
       data-controller="project-ai-chat"
       data-project-ai-chat-ask-url-value="<%= project_ai_generate_project_path %>"
       data-project-ai-chat-jack-image-url-value="<%= asset_path('jack.png') %>">

    <!-- Chat Header -->
    <div class="bg-gradient-to-r from-orange-400 to-red-500 text-white p-6 rounded-t-lg">
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-4">
          <%= image_tag "jack.png", class: "w-16 h-16 rounded-full object-cover border-4 border-white shadow-lg", alt: "Jack" %>
          <div>
            <h2 class="text-2xl font-bold">Jack - Project Planning Expert</h2>
            <p class="text-orange-100">30+ years of experience • Let's plan your project together</p>
          </div>
        </div>

        <!-- Chat Actions -->
        <div class="flex items-center space-x-2">
          <%= link_to "/dashboard", class: "text-orange-100 hover:text-white transition-colors p-2 rounded-lg hover:bg-orange-600", title: "Return to Dashboard" do %>
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"></path>
            </svg>
          <% end %>
          <button class="text-orange-100 hover:text-white transition-colors p-2 rounded-lg hover:bg-orange-600"
                  title="Start New Conversation"
                  data-action="click->project-ai-chat#startNewProject">
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
            </svg>
          </button>
        </div>
      </div>
    </div>

    <!-- Property Selection (if multiple properties) -->
    <% if @properties.size > 1 %>
      <div class="p-4 bg-orange-50 border-b border-orange-200">
        <label class="block text-sm font-medium text-gray-700 mb-2">Which property is this project for?</label>
        <select data-project-ai-chat-target="propertySelect" class="w-full border-gray-300 rounded-md shadow-sm focus:ring-orange-500 focus:border-orange-500">
          <option value="">Choose a property...</option>
          <% @properties.each do |property| %>
            <option value="<%= property.id %>" data-property-name="<%= property.name %>" data-property-type="<%= property.property_type %>">
              <%= property.name %> (<%= property.property_type.humanize %>)
            </option>
          <% end %>
        </select>
      </div>
    <% elsif @properties.size == 1 %>
      <input type="hidden" data-project-ai-chat-target="propertySelect" value="<%= @properties.first.id %>"
             data-property-name="<%= @properties.first.name %>" data-property-type="<%= @properties.first.property_type %>">
    <% else %>
      <div class="p-4 bg-yellow-50 border-b border-yellow-200">
        <p class="text-yellow-800">
          You need to <%= link_to "add a property", new_property_path, class: "font-medium text-yellow-900 underline" %> before planning a project.
        </p>
      </div>
    <% end %>

    <!-- Chat Messages -->
    <div class="flex-1 overflow-y-auto p-6 space-y-4 bg-gray-50" data-project-ai-chat-target="messages">
      <!-- Welcome Message -->
      <div class="flex items-start space-x-3">
        <%= image_tag "jack.png", class: "w-10 h-10 rounded-full object-cover flex-shrink-0", alt: "Jack" %>
        <div class="bg-white rounded-lg p-4 shadow-sm max-w-2xl">
          <p class="text-gray-800">
            Hey there! I'm Jack, and I'm excited to help you plan your next home improvement project.
            I've been in the trades for over 30 years, so I know what it takes to get things done right.
          </p>
          <p class="text-gray-800 mt-2">
            Let's start simple - what kind of project are you thinking about? It could be anything from
            a small repair to a major renovation. Just tell me what's on your mind!
          </p>
        </div>
      </div>
    </div>

    <!-- Quick Starter Questions -->
    <div class="p-4 border-t border-gray-200 bg-white">
      <div class="mb-4">
        <p class="text-sm font-medium text-gray-700 mb-2">Not sure where to start? Try one of these:</p>
        <div class="grid grid-cols-2 gap-2">
          <button class="text-left p-3 bg-orange-50 hover:bg-orange-100 rounded-lg text-sm text-orange-700 transition-colors"
                  data-question="I want to renovate my kitchen"
                  data-action="click->project-ai-chat#quickStart">
            🍳 Kitchen renovation
          </button>
          <button class="text-left p-3 bg-blue-50 hover:bg-blue-100 rounded-lg text-sm text-blue-700 transition-colors"
                  data-question="I need to update my bathroom"
                  data-action="click->project-ai-chat#quickStart">
            🚿 Bathroom update
          </button>
          <button class="text-left p-3 bg-green-50 hover:bg-green-100 rounded-lg text-sm text-green-700 transition-colors"
                  data-question="I want to finish my basement"
                  data-action="click->project-ai-chat#quickStart">
            🏠 Basement finishing
          </button>
          <button class="text-left p-3 bg-purple-50 hover:bg-purple-100 rounded-lg text-sm text-purple-700 transition-colors"
                  data-question="I have some repairs that need fixing"
                  data-action="click->project-ai-chat#quickStart">
            🔧 General repairs
          </button>
        </div>
      </div>
    </div>

    <!-- Chat Input -->
    <div class="p-4 border-t border-gray-200 bg-white rounded-b-lg">
      <form class="flex space-x-3"
            data-project-ai-chat-target="form"
            data-action="submit->project-ai-chat#sendMessage">
        <input type="text"
               class="flex-1 border-gray-300 rounded-full px-4 py-3 focus:ring-orange-500 focus:border-orange-500"
               placeholder="Tell Jack about your project idea..."
               data-project-ai-chat-target="input"
               <%= 'disabled' if @properties.empty? %>>
        <button type="submit"
                class="bg-gradient-to-r from-orange-400 to-red-500 hover:from-orange-500 hover:to-red-600 text-white px-6 py-3 rounded-full font-medium transition-all duration-200 disabled:opacity-50"
                <%= 'disabled' if @properties.empty? %>>
          Send
        </button>
      </form>
    </div>
  </div>
</div>
