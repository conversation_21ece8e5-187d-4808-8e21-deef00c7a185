<div class="min-h-screen bg-gray-50">
  <!-- Header -->
  <div class="bg-white shadow">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="flex justify-between items-center py-6">
        <div>
          <h1 class="text-3xl font-bold text-gray-900">Homeowner Dashboard</h1>
          <p class="text-gray-600">Welcome back, <%= Current.user.name %>!</p>
        </div>
        <div class="flex items-center space-x-4">
          <%= link_to "AI Project Creator", project_ai_path, class: "bg-orange-600 hover:bg-orange-700 text-white px-4 py-2 rounded-lg font-medium transition-colors" %>
          <%= link_to "Add Property", new_property_path, class: "bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg font-medium transition-colors" %>
          <%= render 'shared/user_menu' %>
        </div>
      </div>
    </div>
  </div>

  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Stats Cards -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
      <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
              <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
              </svg>
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">Total Projects</p>
            <p class="text-2xl font-semibold text-gray-900"><%= @project_stats[:total] %></p>
          </div>
        </div>
      </div>

      <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center">
              <svg class="w-5 h-5 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">Draft Projects</p>
            <p class="text-2xl font-semibold text-gray-900"><%= @project_stats[:draft] %></p>
          </div>
        </div>
      </div>

      <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
              <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
              </svg>
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">Active Projects</p>
            <p class="text-2xl font-semibold text-gray-900"><%= @project_stats[:active] %></p>
          </div>
        </div>
      </div>

      <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
              <svg class="w-5 h-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">Completed</p>
            <p class="text-2xl font-semibold text-gray-900"><%= @project_stats[:completed] %></p>
          </div>
        </div>
      </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
      <!-- Recent Projects -->
      <div class="bg-white rounded-lg shadow">
        <div class="px-6 py-4 border-b border-gray-200">
          <h2 class="text-lg font-semibold text-gray-900">Recent Projects</h2>
        </div>
        <div class="p-6">
          <% if @recent_projects.any? %>
            <div class="space-y-4">
              <% @recent_projects.each do |project| %>
                <div class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                  <div class="flex justify-between items-start">
                    <div class="flex-1">
                      <h3 class="font-medium text-gray-900"><%= project.name %></h3>
                      <p class="text-sm text-gray-600 mt-1"><%= project.description %></p>
                      <div class="flex items-center mt-2 space-x-4">
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-<%= project.status == 'active' ? 'green' : project.status == 'draft' ? 'yellow' : 'gray' %>-100 text-<%= project.status == 'active' ? 'green' : project.status == 'draft' ? 'yellow' : 'gray' %>-800">
                          <%= project.status.humanize %>
                        </span>
                        <span class="text-xs text-gray-500">
                          Interest deadline: <%= project.interest_deadline.strftime("%b %d, %Y") %>
                        </span>
                      </div>
                    </div>
                    <div class="ml-4">
                      <%= link_to "View", "#", class: "text-blue-600 hover:text-blue-800 text-sm font-medium" %>
                    </div>
                  </div>
                </div>
              <% end %>
            </div>
          <% else %>
            <div class="text-center py-8">
              <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
              </svg>
              <h3 class="mt-2 text-sm font-medium text-gray-900">No projects yet</h3>
              <p class="mt-1 text-sm text-gray-500">Get started by creating your first project.</p>
              <div class="mt-6">
                <%= link_to "AI Project Creator", project_ai_path, class: "inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-orange-600 hover:bg-orange-700" %>
              </div>
            </div>
          <% end %>
        </div>
      </div>

      <!-- Properties -->
      <div class="bg-white rounded-lg shadow">
        <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
          <h2 class="text-lg font-semibold text-gray-900">Your Properties</h2>
          <%= link_to "View All", properties_path, class: "text-sm text-tradecrews-blue-600 hover:text-tradecrews-blue-500 font-medium" %>
        </div>
        <div class="p-6">
          <% if @properties.any? %>
            <div class="space-y-4">
              <% @properties.each do |property| %>
                <div class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                  <div class="flex items-start">
                    <% if property.image.attached? %>
                      <%= image_tag property.image, class: "w-16 h-16 rounded-lg object-cover" %>
                    <% else %>
                      <div class="w-16 h-16 bg-gray-200 rounded-lg flex items-center justify-center">
                        <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                        </svg>
                      </div>
                    <% end %>
                    <div class="ml-4 flex-1">
                      <h3 class="font-medium text-gray-900"><%= property.name %></h3>
                      <p class="text-sm text-gray-600"><%= property.property_type.humanize %></p>
                      <p class="text-sm text-gray-500 mt-1">
                        <%= property.projects.count %> project<%= 's' if property.projects.count != 1 %>
                      </p>
                    </div>
                    <div>
                      <%= link_to "View", property_path(property), class: "text-blue-600 hover:text-blue-800 text-sm font-medium" %>
                    </div>
                  </div>
                </div>
              <% end %>
            </div>
          <% else %>
            <div class="text-center py-8">
              <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"></path>
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5a2 2 0 012-2h4a2 2 0 012 2v2H8V5z"></path>
              </svg>
              <h3 class="mt-2 text-sm font-medium text-gray-900">No properties yet</h3>
              <p class="mt-1 text-sm text-gray-500">Add your first property to get started.</p>
              <div class="mt-6">
                <%= link_to "Add Property", new_property_path, class: "inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700" %>
              </div>
            </div>
          <% end %>
        </div>
      </div>
    </div>
  </div>
</div>