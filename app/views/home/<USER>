<p style="color: green"><%= notice %></p>

<p>Signed as <%= Current.user.email %></p>

<h2>Login and verification</h2>

<div>
  <%= link_to "Change password", edit_password_path %>
</div>

<div>
  <%= link_to "Change email address", edit_identity_email_path %>
</div>

<div>
  <%= link_to "Two-Factor Authentication", new_two_factor_authentication_profile_totp_path %>
</div>

<% if Current.user.otp_required_for_sign_in? %>
  <div><%= link_to "Recovery Codes", two_factor_authentication_profile_recovery_codes_path %></div>
<% end %>

<%= button_to "Signin as last user", user_masquerade_path(User.last) %>

<h2>Access history</h2>

<div>
  <%= link_to "Devices & Sessions", sessions_path %>
</div>

<div>
  <%= link_to "Activity Log", authentications_events_path %>
</div>

<br>

<%= button_to "Log out", Current.session, method: :delete %>
