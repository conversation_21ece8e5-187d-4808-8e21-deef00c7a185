<div class="min-h-screen bg-gray-50">
  <!-- Header -->
  <div class="bg-white shadow">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="flex justify-between items-center py-6">
        <div>
          <h1 class="text-3xl font-bold text-gray-900">Contractor Dashboard</h1>
          <p class="text-gray-600">Welcome back, <%= Current.user.name %>!</p>
          <% if @contractor_profile %>
            <p class="text-sm text-gray-500"><%= @contractor_profile.name %> - <%= @contractor_profile.trade.name if @contractor_profile.trade %></p>
          <% end %>
        </div>
        <div class="flex items-center space-x-4">
          <%= link_to "View All Projects", "#", class: "bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg font-medium transition-colors" %>
          <%= render 'shared/user_menu' %>
        </div>
      </div>
    </div>
  </div>

  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Stats Cards -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
      <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
              <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2-2v2m8 0V6a2 2 0 012 2v6a2 2 0 01-2 2H8a2 2 0 01-2-2V8a2 2 0 012-2z"></path>
              </svg>
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">Available Projects</p>
            <p class="text-2xl font-semibold text-gray-900"><%= @project_stats[:available] %></p>
          </div>
        </div>
      </div>

      <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center">
              <svg class="w-5 h-5 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
              </svg>
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">Interested</p>
            <p class="text-2xl font-semibold text-gray-900"><%= @project_stats[:interested] %></p>
          </div>
        </div>
      </div>

      <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
              <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"></path>
              </svg>
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">Awarded</p>
            <p class="text-2xl font-semibold text-gray-900"><%= @project_stats[:awarded] %></p>
          </div>
        </div>
      </div>

      <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
              <svg class="w-5 h-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">Completed</p>
            <p class="text-2xl font-semibold text-gray-900"><%= @project_stats[:completed] %></p>
          </div>
        </div>
      </div>
    </div>

    <!-- Available Projects -->
    <div class="bg-white rounded-lg shadow">
      <div class="px-6 py-4 border-b border-gray-200">
        <div class="flex justify-between items-center">
          <h2 class="text-lg font-semibold text-gray-900">Available Projects</h2>
          <span class="text-sm text-gray-500"><%= @available_projects.count %> projects available</span>
        </div>
      </div>
      <div class="p-6">
        <% if @available_projects.any? %>
          <div class="space-y-6">
            <% @available_projects.each do |project| %>
              <div class="border border-gray-200 rounded-lg p-6 hover:shadow-lg transition-shadow">
                <div class="flex justify-between items-start">
                  <div class="flex-1">
                    <div class="flex items-start justify-between">
                      <div>
                        <h3 class="text-lg font-semibold text-gray-900"><%= project.name %></h3>
                        <p class="text-gray-600 mt-1"><%= project.description %></p>
                      </div>
                      <div class="ml-4 text-right">
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800">
                          Active
                        </span>
                      </div>
                    </div>
                    
                    <!-- Property Information -->
                    <div class="mt-4 bg-gray-50 rounded-lg p-4">
                      <h4 class="font-medium text-gray-900 mb-2">Property Details</h4>
                      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <p class="text-sm text-gray-600">
                            <span class="font-medium">Property:</span> <%= project.property.name %>
                          </p>
                          <p class="text-sm text-gray-600">
                            <span class="font-medium">Type:</span> <%= project.property.property_type.humanize %>
                          </p>
                          <p class="text-sm text-gray-600">
                            <span class="font-medium">Owner:</span> <%= project.property.user.name %>
                          </p>
                        </div>
                        <div>
                          <% if project.property.address %>
                            <p class="text-sm text-gray-600">
                              <span class="font-medium">Location:</span>
                              <%= project.property.address.city %>, <%= project.property.address.state %>
                            </p>
                          <% end %>
                        </div>
                      </div>
                    </div>

                    <!-- Project Timeline -->
                    <div class="mt-4 flex items-center space-x-6 text-sm text-gray-600">
                      <div class="flex items-center">
                        <svg class="w-4 h-4 mr-1 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        <span class="font-medium">Interest Deadline:</span>
                        <span class="ml-1 <%= 'text-red-600 font-medium' if project.interest_deadline < 3.days.from_now %>">
                          <%= project.interest_deadline.strftime("%b %d, %Y at %I:%M %p") %>
                        </span>
                      </div>
                      <div class="flex items-center">
                        <svg class="w-4 h-4 mr-1 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                        </svg>
                        <span class="font-medium">Start Date:</span>
                        <span class="ml-1"><%= project.start_date.strftime("%b %d, %Y") %></span>
                      </div>
                    </div>

                    <!-- Tasks -->
                    <% if project.tasks.any? %>
                      <div class="mt-4">
                        <h5 class="text-sm font-medium text-gray-900 mb-2">Required Tasks:</h5>
                        <div class="flex flex-wrap gap-2">
                          <% project.tasks.each do |task| %>
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                              <%= task.name %>
                            </span>
                          <% end %>
                        </div>
                      </div>
                    <% end %>

                    <!-- Interest Count -->
                    <div class="mt-4 flex items-center justify-between">
                      <div class="flex items-center text-sm text-gray-600">
                        <svg class="w-4 h-4 mr-1 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                        </svg>
                        <span><%= project.interest_count %> contractor<%= 's' if project.interest_count != 1 %> interested</span>
                      </div>
                      <div class="flex space-x-3">
                        <%= link_to "View Details", "#", class: "text-blue-600 hover:text-blue-800 font-medium text-sm" %>
                        <%= link_to "Express Interest", "#", class: "bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium text-sm transition-colors" %>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            <% end %>
          </div>
        <% else %>
          <div class="text-center py-12">
            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2-2v2m8 0V6a2 2 0 012 2v6a2 2 0 01-2 2H8a2 2 0 01-2-2V8a2 2 0 012-2z"></path>
            </svg>
            <h3 class="mt-2 text-sm font-medium text-gray-900">No available projects</h3>
            <p class="mt-1 text-sm text-gray-500">Check back later for new project opportunities.</p>
            <div class="mt-6">
              <%= link_to "Refresh", request.path, class: "inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700" %>
            </div>
          </div>
        <% end %>
      </div>
    </div>
  </div>
</div>