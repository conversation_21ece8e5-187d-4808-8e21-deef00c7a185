class Contractor::<PERSON>boardsController < ApplicationController
  before_action :authenticate
  before_action :ensure_contractor

  def show
    @available_projects = Project.active
      .joins(property: :address)
      .includes(:property, :tasks, property: [:user, :address])
      .where("interest_deadline > ?", Time.current)
      .order(:interest_deadline)
    @contractor_profile = Current.user.contractor
    @project_stats = calculate_project_stats
  end

  private

  def ensure_contractor
    redirect_to root_path unless Current.user.contractor?
  end

  def calculate_project_stats
    {
      available: Project.active.where("interest_deadline > ?", Time.current).count,
      interested: 0, # TODO: Implement interest tracking
      awarded: 0,    # TODO: Implement project awards
      completed: 0   # TODO: Implement completed projects tracking
    }
  end
end
