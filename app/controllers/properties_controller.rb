class PropertiesController < ApplicationController
  before_action :authenticate
  before_action :ensure_homeowner
  before_action :set_property, only: [:show, :edit, :update, :destroy]

  def index
    @properties = Current.user.properties.includes(:address).order(:name)
  end

  def show
    @projects = @property.projects.includes(:tasks).order(created_at: :desc)
  end

  def new
    @property = Current.user.properties.build
    @property.build_address
  end

  def create
    @property = Current.user.properties.build(property_params)

    if @property.save
      redirect_to @property, notice: "Property was successfully created."
    else
      render :new, status: :unprocessable_entity
    end
  end

  def edit
    @property.build_address unless @property.address
  end

  def update
    if @property.update(property_params)
      redirect_to @property, notice: "Property was successfully updated."
    else
      render :edit, status: :unprocessable_entity
    end
  end

  def destroy
    @property.destroy
    redirect_to properties_path, notice: "Property was successfully deleted."
  end

  private

  def set_property
    @property = Current.user.properties.find(params[:id])
  end

  def ensure_homeowner
    unless Current.user.homeowner?
      redirect_to root_path, alert: "Only homeowners can manage properties."
    end
  end

  def property_params
    params.require(:property).permit(:name, :property_type, :description, :image,
      address_attributes: [:id, :street, :city, :state, :zip_code])
  end
end