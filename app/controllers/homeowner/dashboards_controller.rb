class Homeowner::DashboardsController < ApplicationController
  before_action :authenticate
  before_action :ensure_homeowner

  def show
    @properties = Current.user.properties.includes(:projects, :address)
    @recent_projects = Current.user.properties.joins(:projects)
      .includes(projects: [:tasks])
      .merge(Project.order(created_at: :desc))
      .limit(5)
      .map(&:projects).flatten
    @project_stats = calculate_project_stats
  end

  private

  def ensure_homeowner
    redirect_to root_path unless Current.user.homeowner?
  end

  def calculate_project_stats
    projects = Current.user.properties.joins(:projects).merge(Project.all)
    {
      total: projects.count,
      draft: projects.merge(Project.draft).count,
      active: projects.merge(Project.active).count,
      completed: projects.merge(Project.completed).count
    }
  end
end
