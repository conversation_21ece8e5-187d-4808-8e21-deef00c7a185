class SessionsController < ApplicationController
  skip_before_action :authenticate, only: %i[new create]

  before_action :set_session, only: :destroy

  def index
    @sessions = Current.user.sessions.order(created_at: :desc)
  end

  def new
  end

  def create
    if (user = User.authenticate_by(email: params[:email], password: params[:password]))
      if user.otp_required_for_sign_in?
        session[:challenge_token] = user.signed_id(purpose: :authentication_challenge, expires_in: 20.minutes)
        redirect_to new_two_factor_authentication_challenge_totp_path
      else
        @session = user.sessions.create!
        cookies.signed.permanent[:session_token] = {value: @session.id, httponly: true}

        redirect_to root_path, notice: "Signed in successfully"
      end
    else
      redirect_to sign_in_path(email_hint: params[:email]), alert: "That email or password is incorrect"
    end
  end

  def destroy
    # <PERSON>le sign out from user menu (no session ID provided)
    if params[:id].blank?
      # Sign out from current session
      if Current.session
        Current.session.destroy
        cookies.delete(:session_token)
        redirect_to sign_in_path, notice: "Signed out successfully"
      else
        redirect_to sign_in_path
      end
    else
      # Handle individual session logout from sessions management page
      @session.destroy
      redirect_to(sessions_path, notice: "That session has been logged out")
    end
  end

  private

  def set_session
    @session = Current.user.sessions.find(params[:id]) if params[:id].present?
  end
end
