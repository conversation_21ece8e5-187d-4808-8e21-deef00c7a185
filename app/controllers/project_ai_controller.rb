class ProjectAiController < ApplicationController
  before_action :authenticate

  def show
    @properties = Current.user.properties
  end

  def generate_project
    message = params[:message]
    property_id = params[:property_id]
    conversation_state = params[:conversation_state] || {}
    stage = params[:stage] || "initial"

    begin
      property = Current.user.properties.find(property_id) if property_id.present?

      response = AiService.conversational_project_planning(
        message: message,
        property: property,
        conversation_state: conversation_state,
        stage: stage
      )

      render json: response
    rescue => e
      Rails.logger.error "Project AI Error: #{e.message}"
      render json: {
        success: false,
        response: "I'm having trouble processing that right now. Could you try rephrasing your request?",
        error: "Failed to process conversation."
      }
    end
  end
end
