module AiAgentHelper
  extend ActiveSupport::Concern

  # Create a default "Jack" AI agent for new users
  def ensure_default_ai_agent
    return if Current.user.ai_agents.exists?

    Current.user.ai_agents.create!(
      name: "<PERSON>",
      provider: :openai,
      model_type: :gpt_4o_mini,
      system_prompt: nil # Will use the default Jack personality
    )
  rescue => e
    Rails.logger.error "Failed to create default AI agent: #{e.message}"
  end
end
