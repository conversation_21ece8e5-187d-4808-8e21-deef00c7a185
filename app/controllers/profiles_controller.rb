class ProfilesController < ApplicationController
  before_action :authenticate

  def show
    @user = Current.user
    @contractor = Current.user.contractor if Current.user.contractor?
    @sessions = Current.user.sessions.order(created_at: :desc).limit(5)
  end

  def edit
    @user = Current.user
    @contractor = Current.user.contractor if Current.user.contractor?
  end

  def update
    @user = Current.user
    @contractor = Current.user.contractor if Current.user.contractor?
    
    # Check if email is being changed and handle verification
    email_changed = @user.email != user_params[:email]
    
    if @user.update(user_params)
      if email_changed
        redirect_to profile_path, notice: "Profile updated successfully. Please check your new email for verification."
      else
        redirect_to profile_path, notice: "Profile updated successfully"
      end
    else
      render :edit, status: :unprocessable_entity
    end
  end

  def update_contractor
    @contractor = Current.user.contractor
    
    if @contractor.update(contractor_params)
      redirect_to profile_path, notice: "Contractor profile updated successfully"
    else
      @user = Current.user
      render :edit, status: :unprocessable_entity
    end
  end

  private

  def user_params
    params.require(:user).permit(:name, :email)
  end

  def contractor_params
    params.require(:contractor).permit(:company_name, :phone, :website, :years_experience, 
                                     :license_number, :insurance_verified, :description, 
                                     trade_ids: [])
  end
end