class ProjectsController < ApplicationController
  before_action :authenticate
  before_action :ensure_homeowner
  before_action :set_project, only: [:show, :edit, :update, :destroy]

  def index
    @projects = Current.user.all_projects.includes(:property, :tasks).order(created_at: :desc)
  end

  def show
    @tasks = @project.tasks.includes(:trade).order(:name)
  end

  def new
    @property = Current.user.properties.find(params[:property_id]) if params[:property_id]
    @project = (@property&.projects || Project).build
    @properties = Current.user.properties.order(:name)
  end

  def create
    @property = Current.user.properties.find(project_params[:property_id])
    @project = @property.projects.build(project_params.except(:property_id))

    if @project.save
      # Create tasks if provided
      if params[:tasks].present?
        create_tasks_from_params(params[:tasks])
      end

      redirect_to @project, notice: "Project was successfully created."
    else
      @properties = Current.user.properties.order(:name)
      render :new, status: :unprocessable_entity
    end
  end

  def edit
    @properties = Current.user.properties.order(:name)
  end

  def update
    if @project.update(project_params)
      redirect_to @project, notice: "Project was successfully updated."
    else
      @properties = Current.user.properties.order(:name)
      render :edit, status: :unprocessable_entity
    end
  end

  def destroy
    @project.destroy
    redirect_to projects_path, notice: "Project was successfully deleted."
  end

  # AJAX endpoint for creating project from AI suggestions
  def create_from_ai
    ai_data = JSON.parse(params[:ai_data])
    @property = Current.user.properties.find(params[:property_id])

    # Create the project
    @project = @property.projects.build(
      name: ai_data["project_name"] || "AI Generated Project",
      description: ai_data["description"] || "",
      start_date: parse_ai_date(ai_data.dig("timeline", "suggested_start_date")) || 1.week.from_now,
      interest_deadline: parse_ai_date(ai_data.dig("timeline", "suggested_start_date"))&.-(3.days) || 4.days.from_now,
      status: :draft
    )

    if @project.save
      # Create tasks from AI suggestions
      if ai_data["tasks"].present?
        create_tasks_from_ai_data(ai_data["tasks"])
      end

      render json: {
        success: true,
        project_id: @project.id,
        redirect_url: project_path(@project)
      }
    else
      render json: {
        success: false,
        error: "Failed to create project: #{@project.errors.full_messages.join(", ")}"
      }
    end
  rescue => e
    Rails.logger.error "Error creating project from AI: #{e.message}"
    render json: {
      success: false,
      error: "Failed to create project. Please try again."
    }
  end

  private

  def set_project
    @project = Project.joins(property: :user)
      .where(properties: {users: {id: Current.user.id}})
      .find(params[:id])
  end

  def ensure_homeowner
    unless Current.user.homeowner?
      redirect_to root_path, alert: "Only homeowners can manage projects."
    end
  end

  def project_params
    params.require(:project).permit(:property_id, :name, :description, :start_date, :interest_deadline, :status)
  end

  def create_tasks_from_params(tasks_data)
    return unless tasks_data.is_a?(Array)

    tasks_data.each do |task_data|
      next unless task_data[:name].present?

      # Find or create trade
      trade = find_or_create_trade(task_data[:trade] || "General")

      @project.tasks.create!(
        name: task_data[:name],
        trade: trade
      )
    end
  end

  def create_tasks_from_ai_data(ai_tasks)
    return unless ai_tasks.is_a?(Array)

    ai_tasks.each do |task_data|
      next unless task_data["name"].present?

      # Find or create trade
      trade = find_or_create_trade(task_data["trade"] || "General")

      @project.tasks.create!(
        name: task_data["name"],
        trade: trade
      )
    end
  end

  def find_or_create_trade(trade_name)
    Trade.find_or_create_by(name: trade_name.strip.titleize)
  end

  def parse_ai_date(date_string)
    return nil if date_string.blank?

    begin
      Date.parse(date_string)
    rescue Date::Error
      nil
    end
  end
end
