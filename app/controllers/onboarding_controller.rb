class OnboardingController < ApplicationController
  before_action :authenticate_user!
  before_action :redirect_if_onboarded
  before_action :ensure_confirmed

  def show
    case current_step
    when "property"
      redirect_to onboarding_property_path
    when "contractor_profile"
      redirect_to onboarding_contractor_profile_path
    else
      redirect_to onboarding_property_path
    end
  end

  def property
    @property = current_user.properties.build
    @property.build_address
  end

  def create_property
    @property = current_user.properties.build(property_params)

    if @property.save
      current_user.update!(onboarded: true)
      redirect_to root_path, notice: "Welcome to TradeCrews! Your property has been added."
    else
      render :property
    end
  end

  def contractor_profile
    @contractor = current_user.build_contractor if current_user.contractor.nil?
    @contractor ||= current_user.contractor
  end

  def create_contractor_profile
    @contractor = current_user.build_contractor(contractor_params)

    if @contractor.save
      current_user.update!(onboarded: true)
      redirect_to root_path, notice: "Welcome to TradeCrews! Your contractor profile is complete."
    else
      render :contractor_profile
    end
  end

  private

  def current_step
    if current_user.homeowner?
      "property"
    elsif current_user.contractor?
      "contractor_profile"
    else
      "property" # default
    end
  end

  def redirect_if_onboarded
    redirect_to root_path if current_user.onboarded?
  end

  def ensure_confirmed
    unless current_user.confirmed?
      redirect_to root_path, alert: "Please confirm your email address before continuing."
    end
  end

  def property_params
    params.require(:property).permit(:name, :property_type, :description, :image,
      address_attributes: [:street, :city, :state, :zip_code])
  end

  def contractor_params
    params.require(:contractor).permit(:company_name, :description, :phone, :website,
      :license_number, :insurance_verified, :years_experience, trade_ids: [])
  end
end
