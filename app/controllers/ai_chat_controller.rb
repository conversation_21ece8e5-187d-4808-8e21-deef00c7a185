class AiChatController < ApplicationController
  before_action :authenticate

  def show
    # General AI chat interface
  end

  def ask
    question = params[:question]

    # Build context from optional parameters
    context = {}
    if params[:property_id].present?
      context[:property] = Current.user.properties.find(params[:property_id])
    end
    if params[:project_id].present?
      context[:project] = Project.joins(property: :user)
        .where(properties: {users: {id: Current.user.id}})
        .find(params[:project_id])
    end

    begin
      response = AiService.ask_question(question, context)
      render json: {success: true, response: response}
    rescue => e
      Rails.logger.error "AI Chat Error: #{e.message}"
      render json: {success: false, error: "Failed to get response. Please try again."}
    end
  end
end
