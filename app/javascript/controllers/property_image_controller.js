import { Controller } from "@hotwired/stimulus"

// Connects to data-controller="property-image"
export default class extends Controller {
  static targets = ["fileInput", "imagePreview", "previewImage", "removeButton", "dropZone"]

  connect() {
    this.dropZoneTarget.addEventListener('dragenter', this.preventDefaults, false)
    this.dropZoneTarget.addEventListener('dragover', this.preventDefaults, false)
    this.dropZoneTarget.addEventListener('dragleave', this.preventDefaults, false)
    this.dropZoneTarget.addEventListener('drop', this.preventDefaults, false)

    this.dropZoneTarget.addEventListener('dragenter', this.highlight, false)
    this.dropZoneTarget.addEventListener('dragover', this.highlight, false)
    this.dropZoneTarget.addEventListener('dragleave', this.unhighlight, false)
    this.dropZoneTarget.addEventListener('drop', this.unhighlight, false)

    this.dropZoneTarget.addEventListener('drop', this.handleDrop.bind(this), false)
    this.fileInputTarget.addEventListener('change', (e) => this.handleFiles(e.target.files))
    this.removeButtonTarget.addEventListener('click', this.removeImage.bind(this))
  }

  preventDefaults(e) {
    e.preventDefault()
    e.stopPropagation()
  }

  highlight = (e) => {
    this.dropZoneTarget.classList.add('border-tradecrews-orange-500', 'bg-tradecrews-orange-50')
  }

  unhighlight = (e) => {
    this.dropZoneTarget.classList.remove('border-tradecrews-orange-500', 'bg-tradecrews-orange-50')
  }

  handleDrop(e) {
    const dt = e.dataTransfer
    const files = dt.files
    this.handleFiles(files)
  }

  handleFiles(files) {
    if (files.length > 0) {
      const file = files[0]
      if (!file.type.startsWith('image/')) {
        alert('Please select an image file.')
        return
      }
      if (file.size > 10 * 1024 * 1024) {
        alert('File size must be less than 10MB.')
        return
      }
      const dataTransfer = new DataTransfer()
      dataTransfer.items.add(file)
      this.fileInputTarget.files = dataTransfer.files
      const reader = new FileReader()
      reader.onload = (e) => {
        this.previewImageTarget.src = e.target.result
        this.imagePreviewTarget.classList.remove('hidden')
        this.dropZoneTarget.classList.add('hidden')
      }
      reader.readAsDataURL(file)
    }
  }

  removeImage() {
    this.fileInputTarget.value = ''
    this.imagePreviewTarget.classList.add('hidden')
    this.dropZoneTarget.classList.remove('hidden')
  }
}
