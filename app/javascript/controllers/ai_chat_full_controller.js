import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static targets = ["messages", "input", "form", "propertySelect", "projectSelect"]
  static values = { 
    askUrl: String,
    jackImageUrl: String
  }

  connect() {
    this.inputTarget.focus()
  }

  async sendMessage(event) {
    event.preventDefault()
    
    const question = this.inputTarget.value.trim()
    if (!question) return
    
    const propertyId = this.propertySelectTarget.value
    const projectId = this.projectSelectTarget.value
    
    // Add user message to chat
    this.addMessage('user', question)
    this.inputTarget.value = ''
    
    // Show typing indicator
    const typingId = this.addMessage('jack', 'Jack is thinking...', true)
    
    try {
      const body = { question: question }
      if (propertyId) body.property_id = propertyId
      if (projectId) body.project_id = projectId
      
      const response = await fetch(this.askUrlValue, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRF-Token': document.querySelector('[name="csrf-token"]').content
        },
        body: JSON.stringify(body)
      })
      
      const result = await response.json()
      
      // Remove typing indicator
      document.getElementById(typingId).remove()
      
      if (result.success) {
        this.addMessage('jack', result.response)
      } else {
        this.addMessage('jack', 'Sorry, I encountered an error. Please try again.')
      }
    } catch (error) {
      console.error('Error:', error)
      document.getElementById(typingId).remove()
      this.addMessage('jack', 'Sorry, I encountered an error. Please try again.')
    }
  }
  
  addMessage(sender, message, isTyping = false) {
    const messageId = 'msg-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9)
    const isUser = sender === 'user'
    
    const messageDiv = document.createElement('div')
    messageDiv.id = messageId
    messageDiv.className = `flex items-start space-x-3 ${isUser ? 'flex-row-reverse space-x-reverse' : ''}`
    
    if (isUser) {
      messageDiv.innerHTML = `
        <div class="w-8 h-8 bg-gray-600 rounded-full flex items-center justify-center flex-shrink-0">
          <span class="text-sm font-bold text-white">You</span>
        </div>
        <div class="bg-blue-600 text-white rounded-lg p-3 max-w-md">
          <p class="text-sm">${message}</p>
        </div>
      `
    } else {
      messageDiv.innerHTML = `
        <div class="w-8 h-8 bg-gradient-to-br from-blue-400 to-blue-600 rounded-full flex items-center justify-center flex-shrink-0">
          <span class="text-sm font-bold text-white">AI</span>
        </div>
        <div class="bg-gray-100 rounded-lg p-3 max-w-md">
          <p class="text-gray-800 ${isTyping ? 'italic text-gray-500' : ''}">${message}</p>
        </div>
      `
    }
    
    this.messagesTarget.appendChild(messageDiv)
    this.messagesTarget.scrollTop = this.messagesTarget.scrollHeight
    
    return messageId
  }
}