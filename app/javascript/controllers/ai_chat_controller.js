import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static targets = ["interface", "messages", "input", "form"]
  static values = { 
    askUrl: String,
    jackImageUrl: String
  }

  connect() {
    this.isMinimized = false
    this.bindOutsideClick()
  }

  disconnect() {
    this.unbindOutsideClick()
  }

  toggle(event) {
    event.stopPropagation()
    
    if (this.interfaceTarget.classList.contains('hidden')) {
      this.open()
    } else {
      this.close()
    }
  }

  open() {
    this.interfaceTarget.classList.remove('hidden')
    this.interfaceTarget.classList.add('flex', 'flex-col')
    this.inputTarget.focus()
  }

  close() {
    this.interfaceTarget.classList.add('hidden')
    this.interfaceTarget.classList.remove('flex', 'flex-col')
    this.isMinimized = false
  }

  minimize() {
    if (this.isMinimized) {
      // Maximize
      this.interfaceTarget.style.height = '500px'
      this.messagesTarget.style.display = 'block'
      this.element.querySelector('.px-4.py-2').style.display = 'block'
      this.element.querySelector('.p-4.border-t').style.display = 'block'
      this.element.querySelector('#minimize-chat svg').innerHTML = '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 12H4"></path>'
      this.isMinimized = false
    } else {
      // Minimize
      this.interfaceTarget.style.height = '60px'
      this.messagesTarget.style.display = 'none'
      this.element.querySelector('.px-4.py-2').style.display = 'none'
      this.element.querySelector('.p-4.border-t').style.display = 'none'
      this.element.querySelector('#minimize-chat svg').innerHTML = '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 15l7-7 7 7"></path>'
      this.isMinimized = true
    }
  }

  async submitMessage(event) {
    event.preventDefault()
    
    const question = this.inputTarget.value.trim()
    if (!question) return
    
    // Add user message
    this.addMessage('user', question)
    this.inputTarget.value = ''
    
    // Add typing indicator
    const typingId = this.addMessage('jack', 'Jack is thinking...', true)
    
    try {
      const response = await fetch(this.askUrlValue, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRF-Token': document.querySelector('[name="csrf-token"]').content
        },
        body: JSON.stringify({ question: question })
      })
      
      const result = await response.json()
      
      // Remove typing indicator
      document.getElementById(typingId).remove()
      
      if (result.success) {
        this.addMessage('jack', result.response)
      } else {
        this.addMessage('jack', 'Sorry, I encountered an error. Please try again.')
      }
    } catch (error) {
      console.error('Error:', error)
      document.getElementById(typingId).remove()
      this.addMessage('jack', 'Sorry, I encountered an error. Please try again.')
    }
  }

  quickAction(event) {
    const question = event.currentTarget.dataset.question
    this.inputTarget.value = question
    this.submitMessage(new Event('submit'))
  }

  preventClose(event) {
    event.stopPropagation()
  }

  addMessage(sender, message, isTyping = false) {
    const messageId = 'msg-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9)
    const isUser = sender === 'user'
    
    const messageDiv = document.createElement('div')
    messageDiv.id = messageId
    messageDiv.className = `flex items-start space-x-3 ${isUser ? 'flex-row-reverse space-x-reverse' : ''}`
    
    if (isUser) {
      messageDiv.innerHTML = `
        <div class="w-8 h-8 bg-gray-600 rounded-full flex items-center justify-center flex-shrink-0">
          <span class="text-sm font-bold text-white">You</span>
        </div>
        <div class="bg-gradient-to-r from-orange-400 to-red-500 text-white rounded-lg p-3 shadow-sm max-w-xs">
          <p class="text-sm">${message}</p>
        </div>
      `
    } else {
      messageDiv.innerHTML = `
        <img src="${this.jackImageUrlValue}" class="w-8 h-8 rounded-full object-cover flex-shrink-0" alt="Jack">
        <div class="bg-white rounded-lg p-3 shadow-sm max-w-xs">
          <p class="text-gray-800 text-sm ${isTyping ? 'italic text-gray-500' : ''}">${message}</p>
        </div>
      `
    }
    
    this.messagesTarget.appendChild(messageDiv)
    this.messagesTarget.scrollTop = this.messagesTarget.scrollHeight
    
    return messageId
  }

  bindOutsideClick() {
    this.outsideClickHandler = this.handleOutsideClick.bind(this)
    document.addEventListener('click', this.outsideClickHandler)
  }

  unbindOutsideClick() {
    if (this.outsideClickHandler) {
      document.removeEventListener('click', this.outsideClickHandler)
    }
  }

  handleOutsideClick(event) {
    if (!this.interfaceTarget.classList.contains('hidden')) {
      // Check if click is outside the chat interface and FAB
      if (!this.element.contains(event.target)) {
        this.close()
      }
    }
  }
}