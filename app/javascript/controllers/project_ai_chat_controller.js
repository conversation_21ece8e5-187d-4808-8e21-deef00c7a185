import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static targets = ["messages", "input", "form", "propertySelect"]
  static values = { 
    askUrl: String,
    jackImageUrl: String
  }

  connect() {
    this.conversationState = {
      stage: 'initial', // initial, gathering_requirements, confirming_details, creating_project
      projectData: {},
      propertyId: null,
      propertyName: null,
      propertyType: null
    }
  }

  quickStart(event) {
    const question = event.currentTarget.dataset.question
    this.inputTarget.value = question
    this.sendMessage(new Event('submit'))
  }

  async sendMessage(event) {
    event.preventDefault()
    
    const message = this.inputTarget.value.trim()
    if (!message) return

    // Check if property is selected (for multiple properties)
    if (this.hasPropertySelectTarget && this.propertySelectTarget.value) {
      const selectedOption = this.propertySelectTarget.selectedOptions[0]
      this.conversationState.propertyId = this.propertySelectTarget.value
      this.conversationState.propertyName = selectedOption.dataset.propertyName
      this.conversationState.propertyType = selectedOption.dataset.propertyType
    } else if (this.hasPropertySelectTarget && this.propertySelectTarget.type === 'hidden') {
      // Single property case
      this.conversationState.propertyId = this.propertySelectTarget.value
      this.conversationState.propertyName = this.propertySelectTarget.dataset.propertyName
      this.conversationState.propertyType = this.propertySelectTarget.dataset.propertyType
    }

    // Add user message
    this.addMessage('user', message)
    this.inputTarget.value = ''
    
    // Add typing indicator
    const typingId = this.addMessage('jack', 'Jack is thinking about your project...', true)
    
    try {
      const response = await fetch(this.askUrlValue, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRF-Token': document.querySelector('[name="csrf-token"]').content
        },
        body: JSON.stringify({
          message: message,
          property_id: this.conversationState.propertyId,
          conversation_state: this.conversationState,
          stage: this.conversationState.stage
        })
      })
      
      const result = await response.json()
      
      // Remove typing indicator
      document.getElementById(typingId).remove()
      
      if (result.success) {
        // Update conversation state if provided
        if (result.conversation_state) {
          this.conversationState = { ...this.conversationState, ...result.conversation_state }
        }
        
        this.addMessage('jack', result.response)
        
        // Handle special actions
        if (result.action) {
          this.handleAction(result.action, result.data)
        }
      } else {
        this.addMessage('jack', 'Sorry, I encountered an error. Let me try to help you another way.')
      }
    } catch (error) {
      console.error('Error:', error)
      document.getElementById(typingId).remove()
      this.addMessage('jack', 'Sorry, I had trouble processing that. Could you try rephrasing your request?')
    }
  }

  handleAction(action, data) {
    switch (action) {
      case 'show_project_summary':
        this.showProjectSummary(data)
        break
      case 'create_project':
        this.createProject(data)
        break
      case 'request_clarification':
        this.showClarificationOptions(data)
        break
    }
  }

  showProjectSummary(projectData) {
    const summaryHtml = `
      <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mt-4">
        <h4 class="font-semibold text-blue-900 mb-2">📋 Project Summary</h4>
        <div class="space-y-2 text-sm">
          <p><strong>Project:</strong> ${projectData.name || 'Your Project'}</p>
          <p><strong>Property:</strong> ${this.conversationState.propertyName}</p>
          <p><strong>Estimated Duration:</strong> ${projectData.duration || 'TBD'}</p>
          <p><strong>Complexity:</strong> ${projectData.complexity || 'TBD'}</p>
        </div>
        <div class="mt-3 flex space-x-2">
          <button class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded text-sm font-medium"
                  data-action="click->project-ai-chat#confirmProject">
            ✅ Looks Good - Create Project
          </button>
          <button class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded text-sm font-medium"
                  data-action="click->project-ai-chat#modifyProject">
            ✏️ I want to modify something
          </button>
        </div>
      </div>
    `
    
    const lastMessage = this.messagesTarget.lastElementChild
    lastMessage.querySelector('.bg-white').insertAdjacentHTML('beforeend', summaryHtml)
  }

  showClarificationOptions(options) {
    const optionsHtml = `
      <div class="mt-3 space-y-2">
        ${options.map(option => `
          <button class="block w-full text-left p-2 bg-orange-50 hover:bg-orange-100 rounded border border-orange-200 text-sm"
                  data-question="${option.question}"
                  data-action="click->project-ai-chat#selectOption">
            ${option.text}
          </button>
        `).join('')}
      </div>
    `
    
    const lastMessage = this.messagesTarget.lastElementChild
    lastMessage.querySelector('.bg-white').insertAdjacentHTML('beforeend', optionsHtml)
  }

  selectOption(event) {
    const question = event.currentTarget.dataset.question
    this.inputTarget.value = question
    this.sendMessage(new Event('submit'))
  }

  confirmProject() {
    this.inputTarget.value = "Yes, create this project as planned"
    this.sendMessage(new Event('submit'))
  }

  modifyProject() {
    this.inputTarget.value = "I'd like to modify the project details"
    this.sendMessage(new Event('submit'))
  }

  async createProject(projectData) {
    try {
      const response = await fetch('/projects/create_from_ai', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRF-Token': document.querySelector('[name="csrf-token"]').content
        },
        body: JSON.stringify({
          project_data: projectData,
          property_id: this.conversationState.propertyId
        })
      })
      
      const result = await response.json()
      
      if (result.success) {
        this.addMessage('jack', `🎉 Great! I've created your project "${projectData.name}". You can view and manage it from your dashboard.`)
        
        // Show success actions
        const successHtml = `
          <div class="bg-green-50 border border-green-200 rounded-lg p-4 mt-4">
            <div class="flex space-x-2">
              <a href="${result.project_url}" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded text-sm font-medium">
                View Project
              </a>
              <button class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded text-sm font-medium"
                      data-action="click->project-ai-chat#startNewProject">
                Plan Another Project
              </button>
            </div>
          </div>
        `
        
        const lastMessage = this.messagesTarget.lastElementChild
        lastMessage.querySelector('.bg-white').insertAdjacentHTML('beforeend', successHtml)
      } else {
        this.addMessage('jack', 'I had trouble creating the project. Let me try a different approach.')
      }
    } catch (error) {
      console.error('Error creating project:', error)
      this.addMessage('jack', 'Sorry, there was an issue creating the project. Would you like to try again?')
    }
  }

  startNewProject() {
    // Reset conversation state
    this.conversationState = {
      stage: 'initial',
      projectData: {},
      propertyId: this.conversationState.propertyId, // Keep property selection
      propertyName: this.conversationState.propertyName,
      propertyType: this.conversationState.propertyType
    }
    
    this.addMessage('jack', "Great! What's your next project idea? I'm ready to help you plan something new.")
  }

  addMessage(sender, message, isTyping = false) {
    const messageId = 'msg-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9)
    const isUser = sender === 'user'
    
    const messageDiv = document.createElement('div')
    messageDiv.id = messageId
    messageDiv.className = `flex items-start space-x-3 ${isUser ? 'flex-row-reverse space-x-reverse' : ''}`
    
    if (isUser) {
      messageDiv.innerHTML = `
        <div class="w-10 h-10 bg-gray-600 rounded-full flex items-center justify-center flex-shrink-0">
          <span class="text-sm font-bold text-white">You</span>
        </div>
        <div class="bg-gradient-to-r from-orange-400 to-red-500 text-white rounded-lg p-4 shadow-sm max-w-2xl">
          <p class="text-sm">${message}</p>
        </div>
      `
    } else {
      messageDiv.innerHTML = `
        <img src="${this.jackImageUrlValue}" class="w-10 h-10 rounded-full object-cover flex-shrink-0" alt="Jack">
        <div class="bg-white rounded-lg p-4 shadow-sm max-w-2xl">
          <p class="text-gray-800 ${isTyping ? 'italic text-gray-500' : ''}">${message}</p>
        </div>
      `
    }
    
    this.messagesTarget.appendChild(messageDiv)
    this.messagesTarget.scrollTop = this.messagesTarget.scrollHeight
    
    return messageId
  }
}