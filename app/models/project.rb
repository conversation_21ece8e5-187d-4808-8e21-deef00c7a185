# == Schema Information
#
# Table name: projects
#
#  id                :uuid             not null, primary key
#  description       :string           not null
#  interest_count    :integer          default(0), not null
#  interest_deadline :datetime         not null
#  name              :string           not null
#  start_date        :datetime         not null
#  status            :integer          default("draft"), not null
#  created_at        :datetime         not null
#  updated_at        :datetime         not null
#  property_id       :uuid             not null
#
# Indexes
#
#  index_projects_on_property_id  (property_id)
#
# Foreign Keys
#
#  fk_rails_...  (property_id => properties.id)
#
class Project < ApplicationRecord
  belongs_to :property
  has_many :tasks

  enum :status, {draft: 0, active: 1, awarded: 2, completed: 3, cancelled: 4}

  validates :name, :description, :start_date, :interest_deadline, :status, presence: true
  validates_date :start_date, :interest_deadline, on_or_after: :today
  validates_date :start_date, after: :interest_deadline
end
