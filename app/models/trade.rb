# == Schema Information
#
# Table name: trades
#
#  id         :uuid             not null, primary key
#  name       :string           not null
#  quick_hire :boolean          default(FALSE), not null
#  see_all    :boolean          default(FALSE), not null
#  created_at :datetime         not null
#  updated_at :datetime         not null
#  parent_id  :uuid
#
class Trade < ApplicationRecord
  belongs_to :parent, class_name: "Trade", optional: true
  has_many :contractors
  has_many :children, class_name: "Trade", foreign_key: "parent_id"
  has_many :tasks

  validates :name, presence: true
end
