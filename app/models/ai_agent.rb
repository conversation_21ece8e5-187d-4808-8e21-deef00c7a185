# AI Agent model for handling AI-powered project creation and assistance
# == Schema Information
#
# Table name: ai_agents
#
#  id            :uuid             not null, primary key
#  api_key       :string
#  model_type    :integer          default("gpt_4o"), not null
#  name          :string           default("My AI Assistant"), not null
#  provider      :integer          default("openai"), not null
#  settings      :json
#  system_prompt :text
#  created_at    :datetime         not null
#  updated_at    :datetime         not null
#  user_id       :uuid             not null
#
# Indexes
#
#  index_ai_agents_on_user_id               (user_id)
#  index_ai_agents_on_user_id_and_provider  (user_id,provider)
#
# Foreign Keys
#
#  fk_rails_...  (user_id => users.id)
#
class AiAgent < ApplicationRecord
  belongs_to :user

  enum :provider, {openai: 0, anthropic: 1}
  enum :model_type, {
    gpt_4o: 0,
    gpt_4o_mini: 1,
    claude_3_5_sonnet: 2,
    claude_3_5_haiku: 3
  }

  validates :provider, :model_type, presence: true
  validates :api_key, presence: true, if: :requires_api_key?

  # Default configurations for each model
  MODEL_CONFIGS = {
    gpt_4o: {provider: :openai, model_name: "gpt-4o", max_tokens: 4096},
    gpt_4o_mini: {provider: :openai, model_name: "gpt-4o-mini", max_tokens: 16384},
    claude_3_5_sonnet: {provider: :anthropic, model_name: "claude-3-5-sonnet-20241022", max_tokens: 8192},
    claude_3_5_haiku: {provider: :anthropic, model_name: "claude-3-5-haiku-20241022", max_tokens: 8192}
  }.freeze

  def client
    case provider.to_sym
    when :openai
      @openai_client ||= OpenAI::Client.new(
        api_key: api_key || Rails.application.credentials.openai[:api_key]
      )
    when :anthropic
      @anthropic_client ||= Anthropic::Client.new(
        api_key: api_key || ENV["ANTHROPIC_API_KEY"]
      )
    end
  end

  def model_config
    MODEL_CONFIGS[model_type.to_sym]
  end

  def generate_project_suggestions(property, requirements)
    prompt = build_project_prompt(property, requirements)

    case provider.to_sym
    when :openai
      generate_openai_response(prompt)
    when :anthropic
      generate_anthropic_response(prompt)
    end
  end

  def answer_question(question, context = {})
    prompt = build_question_prompt(question, context)

    case provider.to_sym
    when :openai
      generate_openai_response(prompt)
    when :anthropic
      generate_anthropic_response(prompt)
    end
  end

  private

  def get_jack_personality
    custom_prompt = system_prompt.presence
    return custom_prompt if custom_prompt

    <<~PERSONALITY
      You are Jack, a wise and experienced homeowner who has spent decades working on your own home and helping neighbors with their projects. You have a warm, fatherly personality and speak from genuine experience.

      Your background:
      - You've renovated multiple rooms in your own home over the years
      - You've learned from both successes and mistakes
      - You have practical knowledge of tools, materials, and techniques
      - You understand budgets and the importance of doing things right the first time
      - You've worked with contractors and know how to manage projects
      - You care about safety and quality workmanship

      Your communication style:
      - Warm and encouraging, like talking to a friend or family member
      - Share personal anecdotes and lessons learned
      - Practical and realistic about timelines and costs
      - Always emphasize safety and proper preparation
      - Honest about when to DIY vs when to call professionals
      - Use accessible language, not overly technical jargon

      You genuinely want to help people succeed with their home projects and avoid the mistakes you've made along the way.
    PERSONALITY
  end

  def requires_api_key?
    # Only require API key if no environment variables are set
    ENV["OPENAI_API_KEY"].blank? && ENV["ANTHROPIC_API_KEY"].blank?
  end

  def build_project_prompt(property, requirements)
    base_personality = get_jack_personality

    <<~PROMPT
      #{base_personality}

      I'm here to help you plan a project for your property. Let me share what I've learned from my years of working on homes.

      Property Information:
      - Name: #{property.name}
      - Type: #{property.property_type}
      - Description: #{property.description}
      - Address: #{property.address&.full_address}

      What you're looking to accomplish: #{requirements}

      Based on my experience, let me create a practical project plan for you. I'll break this down the way I would approach it myself, thinking about the logical sequence, potential challenges I've encountered before, and realistic timelines.

      Please provide your response as JSON with this structure:
      {
        "project_name": "string",
        "description": "string (include personal insights and tips from experience)",
        "timeline": {
          "estimated_duration_days": number,
          "suggested_start_date": "YYYY-MM-DD"
        },
        "tasks": [
          {
            "name": "string",
            "trade": "string",
            "description": "string (include practical tips)",
            "estimated_duration_days": number
          }
        ],
        "complexity": "low|medium|high",
        "considerations": ["string (include lessons learned and practical warnings)"]
      }
    PROMPT
  end

  def build_question_prompt(question, context)
    base_personality = get_jack_personality

    context_info = ""
    if context[:property]
      context_info += "Property: #{context[:property].name} (#{context[:property].property_type})\n"
    end
    if context[:project]
      context_info += "Project: #{context[:project].name} - #{context[:project].description}\n"
    end

    <<~PROMPT
      #{base_personality}

      #{context_info.present? ? "I can see you're asking about your #{context_info.strip}. " : ""}

      Your question: #{question}

      Let me share what I've learned from my years of working on homes and projects. I'll give you practical advice based on real experience - the kind of wisdom you pick up after making a few mistakes and learning the right way to do things.

      I'll be honest about potential challenges, share cost-effective approaches, and help you avoid the pitfalls I've encountered. Think of this as advice from a neighbor who's been through similar projects and wants to help you succeed.
    PROMPT
  end

  def generate_openai_response(prompt)
    config = model_config

    response = client.chat.completions.create(
      model: config[:model_name],
      messages: [
        {role: "user", content: prompt}
      ],
      max_tokens: config[:max_tokens],
      temperature: 0.7
    )

    response.choices.first.message.content
  rescue => e
    Rails.logger.error "OpenAI API Error: #{e.message}"
    "I apologize, but I'm having trouble processing your request right now. Please try again later."
  end

  def generate_anthropic_response(prompt)
    config = model_config

    response = client.messages.create(
      model: config[:model_name],
      max_tokens: config[:max_tokens],
      messages: [
        {role: "user", content: prompt}
      ]
    )

    response.content.first.text
  rescue => e
    Rails.logger.error "Anthropic API Error: #{e.message}"
    "I apologize, but I'm having trouble processing your request right now. Please try again later."
  end
end
