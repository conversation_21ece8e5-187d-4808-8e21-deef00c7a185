# == Schema Information
#
# Table name: properties
#
#  id            :uuid             not null, primary key
#  deleted_at    :datetime
#  description   :string
#  name          :string           not null
#  property_type :integer          default("single_family"), not null
#  created_at    :datetime         not null
#  updated_at    :datetime         not null
#  user_id       :uuid             not null
#
# Indexes
#
#  index_properties_on_user_id  (user_id)
#
# Foreign Keys
#
#  fk_rails_...  (user_id => users.id)
#
class Property < ApplicationRecord
  belongs_to :user
  has_one :address, as: :addressable, dependent: :destroy
  has_many :projects, dependent: :destroy

  has_one_attached :image

  accepts_nested_attributes_for :address, allow_destroy: true

  enum :property_type, {single_family: 0, condo: 1, townhouse: 2, multi_family: 3, mobile_home: 4, other: 5}

  validates :name, presence: true
  validates :image, content_type: ["image/png", "image/jpeg", "image/webp"], size: {less_than: 10.megabytes}, allow_blank: true
end
