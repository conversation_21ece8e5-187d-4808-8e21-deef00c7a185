# == Schema Information
#
# Table name: users
#
#  id                       :uuid             not null, primary key
#  current_sign_in_at       :datetime
#  current_sign_in_ip       :string
#  email                    :string           default(""), not null
#  last_sign_in_at          :datetime
#  last_sign_in_ip          :string
#  name                     :string           not null
#  onboarded                :boolean          default(FALSE), not null
#  otp_required_for_sign_in :boolean          default(FALSE), not null
#  otp_secret               :string           default(""), not null
#  password_digest          :string           default(""), not null
#  provider                 :string
#  role                     :integer          default("homeowner"), not null
#  sign_in_count            :integer          default(0), not null
#  uid                      :string
#  verified                 :boolean          default(FALSE), not null
#  created_at               :datetime         not null
#  updated_at               :datetime         not null
#
# Indexes
#
#  index_users_on_email             (email) UNIQUE
#  index_users_on_provider_and_uid  (provider,uid) UNIQUE
#
class User < ApplicationRecord
  has_secure_password

  has_many :properties
  has_many :sessions, dependent: :destroy
  has_many :recovery_codes, dependent: :destroy
  has_many :events, dependent: :destroy
  has_one :contractor, dependent: :destroy
  has_many :ai_agents, dependent: :destroy

  enum :role, {homeowner: 0, contractor: 1, admin: 2}

  normalizes :email, with: -> { it.strip.downcase }

  before_validation if: :email_changed?, on: :update do
    self.verified = false
  end

  before_validation on: :create do
    self.otp_secret = ROTP::Base32.random
  end

  validates :name, presence: true
  validates :email, presence: true, uniqueness: {case_sensitive: false}, format: {with: URI::MailTo::EMAIL_REGEXP}
  validates :password, presence: true, length: {minimum: 6}, on: :create, unless: :oauth_user?
  validates :password_confirmation, presence: true, length: {minimum: 6}, confirmation: true, on: :create, unless: :oauth_user?
  validates :password, not_pwned: {message: "might easily be guessed"}, unless: -> { Rails.env.test? }

  generates_token_for :email_verification, expires_in: 2.days do
    email
  end

  generates_token_for :password_reset, expires_in: 20.minutes do
    password_salt&.last(10) || email
  end

  after_update if: :password_digest_previously_changed? do
    sessions.where.not(id: Current.session).delete_all
  end

  after_update if: :email_previously_changed? do
    events.create! action: "email_verification_requested"
  end

  after_update if: :password_digest_previously_changed? do
    events.create! action: "password_changed"
  end

  after_update if: [:verified_previously_changed?, :verified?] do
    events.create! action: "email_verified"
  end

  # Dashboard helper methods
  def all_projects
    return Project.none unless homeowner?
    properties.joins(:projects).merge(Project.all)
  end

  def project_stats
    return {} unless homeowner?
    projects = all_projects
    {
      total: projects.count,
      draft: projects.merge(Project.draft).count,
      active: projects.merge(Project.active).count,
      completed: projects.merge(Project.completed).count
    }
  end

  private

  def oauth_user?
    provider.present? && uid.present?
  end
end
