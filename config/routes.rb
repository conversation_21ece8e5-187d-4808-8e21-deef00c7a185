Rails.application.routes.draw do
  get "sign_in", to: "sessions#new"
  post "sign_in", to: "sessions#create"
  delete "sign_out", to: "sessions#destroy"
  get "sign_up", to: "registrations#new"
  post "sign_up", to: "registrations#create"
  get "sign_up/homeowner", to: "registrations#new", defaults: {role: "homeowner"}, as: :sign_up_homeowner
  get "sign_up/contractor", to: "registrations#new", defaults: {role: "contractor"}, as: :sign_up_contractor

  resources :sessions, only: [:index, :show, :destroy]
  resource :password, only: [:edit, :update]
  resource :profile, only: [:show, :edit, :update] do
    patch :update_contractor, on: :member
  end

  namespace :identity do
    resource :email, only: [:edit, :update]
    resource :email_verification, only: [:show, :create]
    resource :password_reset, only: [:new, :edit, :create, :update]
  end

  namespace :authentications do
    resources :events, only: :index
  end

  namespace :two_factor_authentication do
    namespace :challenge do
      resource :totp, only: [:new, :create]
      resource :recovery_codes, only: [:new, :create]
    end

    namespace :profile do
      resource :totp, only: [:new, :create, :update]
      resources :recovery_codes, only: [:index, :create]
    end
  end

  get "/auth/failure", to: "sessions/omniauth#failure"
  get "/auth/:provider/callback", to: "sessions/omniauth#create"
  post "/auth/:provider/callback", to: "sessions/omniauth#create"
  post "users/:user_id/masquerade", to: "masquerades#create", as: :user_masquerade

  namespace :sessions do
    resource :sudo, only: [:new, :create]
  end

  # Dashboard routes
  get "/dashboard", to: "dashboards#show"
  get "/dashboard/homeowner", to: "homeowner/dashboards#show", as: :homeowner_dashboard
  get "/dashboard/contractor", to: "contractor/dashboards#show", as: :contractor_dashboard

  # Properties routes
  resources :properties

  # Projects routes
  resources :projects do
    collection do
      post :create_from_ai
    end
  end

  # AI Chat routes
  get "/ai_chat", to: "ai_chat#show"
  post "/ai_chat/ask", to: "ai_chat#ask"

  # Project AI routes
  get "/project_ai", to: "project_ai#show"
  post "/project_ai/generate_project", to: "project_ai#generate_project"

  # Onboarding routes
  get "/onboarding", to: "onboarding#show"
  get "/onboarding/property", to: "onboarding#property"
  post "/onboarding/property", to: "onboarding#create_property"
  get "/onboarding/contractor_profile", to: "onboarding#contractor_profile"
  post "/onboarding/contractor_profile", to: "onboarding#create_contractor_profile"

  # Define your application routes per the DSL in https://guides.rubyonrails.org/routing.html

  # Reveal health status on /up that returns 200 if the app boots with no exceptions, otherwise 500.
  # Can be used by load balancers and uptime monitors to verify that the app is live.
  get "up" => "rails/health#show", :as => :rails_health_check

  # Render dynamic PWA files from app/views/pwa/* (remember to link manifest in application.html.erb)
  # get "manifest" => "rails/pwa#manifest", as: :pwa_manifest
  # get "service-worker" => "rails/pwa#service_worker", as: :pwa_service_worker

  # Defines the root path route ("/")
  root "static#index"
end
