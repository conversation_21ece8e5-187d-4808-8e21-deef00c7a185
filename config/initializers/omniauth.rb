Rails.application.config.middleware.use OmniAuth::Builder do
  provider :developer unless Rails.env.production? # You should replace it with your provider

  # Only configure OAuth providers if credentials are available (skip in test environment)
  if Rails.application.credentials.google&.dig(:client_id) && Rails.application.credentials.google.dig(:client_secret)
    provider :google_oauth2, Rails.application.credentials.google[:client_id], Rails.application.credentials.google[:client_secret], {
      scope: ["email", "profile"]
    }
  end

  if Rails.application.credentials.entra_id&.dig(:client_id) && Rails.application.credentials.entra_id.dig(:client_secret)
    provider :entra_id, {client_id: Rails.application.credentials.entra_id[:client_id], client_secret: Rails.application.credentials.entra_id[:client_secret]}, {
      scope: ["email", "profile", "openid"]
    }
  end
end
